<template>
  <div>
    <!-- 控制按钮组 -->
    <img
      v-for="button in controlButtons"
      :key="button.key"
      :class="button.class"
      :src="button.src"
      :alt="button.alt"
      :title="button.title"
      :width="button.width"
      :style="[buttonCommonStyle, button.disabled ? { opacity: 0.5, cursor: 'not-allowed' } : {}]"
      @click="handleButtonClick(button)"
    />

    <!-- 控制面板 -->
    <div class="panel-controls" v-if="showPanel" :style="panelStyle">
      <div class="left">
        <el-row
          class="item"
          @click="store.commit('SET_ScenarioShow', !store.state.app.ifScenarioShow)"
        >
          <el-icon class="icon"
            ><View v-if="store.state.app.ifScenarioShow" /><Hide v-else
          /></el-icon>
          方案信息
        </el-row>
        <el-row
          class="item"
          @click="store.commit('SET_LogBoxShow', !store.state.app.ifLogBoxShow)"
        >
          <el-icon class="icon"
            ><View v-if="store.state.app.ifLogBoxShow" /><Hide v-else
          /></el-icon>
          日志
        </el-row>
        <el-row
          class="item"
          @click="store.commit('SET_EntityListBoxShow', !store.state.app.entityListShow)"
        >
          <el-icon class="icon"
            ><View v-if="store.state.app.entityListShow" /><Hide v-else
          /></el-icon>
          实体列表
        </el-row>
        <el-row
          class="item"
          @click="store.commit('SET_EntityInfoShow', !store.state.app.EntityInfo)"
        >
          <el-icon class="icon"
            ><View v-if="store.state.app.EntityInfo" /><Hide v-else
          /></el-icon>
          实体信息
        </el-row>
        <el-row
          class="item"
          @click="store.commit('SET_EffectBoxShow', !store.state.app.effectBoxShow)"
        >
          <el-icon class="icon"
            ><View v-if="store.state.app.effectBoxShow" /><Hide v-else
          /></el-icon>
          实体配置
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useStore } from 'vuex'
import { View, Hide } from '@element-plus/icons-vue'

const props = defineProps<{
  maxDistance: number;
  center: [number, number];
  existEntity: any[];
  entitiesTypeList: any[];
}>();
const store = useStore()
const isFullscreen = ref(false)
const showPanel = ref(false)

// 通用按钮样式计算
const buttonCommonStyle = computed(() => ({
  right: store.state.app.effectBoxShow || store.state.app.EntityInfo ? '350px' : '7px'
}))

// 按钮禁用状态计算
const isControlButtonDisabled = computed(
  () =>
    store.state.simulationConfig.taskStatus !== 2 ||
    store.state.simulationConfig.runMode === 1
)

// 控制按钮配置数组
const controlButtons = computed(() => [
  {
    key: 'cameraReset',
    class: 'cameraReset',
    src: '/image/camera_reset_icon.png',
    title: '重置相机',
    width: undefined,
    disabled: false,
    action: 'cameraReset'
  },
  {
    key: 'globalSimConfig',
    class: 'globalSimConfig',
    src: '/image/global_sim_config.png',
    title: '态势显示配置',
    width: undefined,
    disabled: false,
    action: 'globalSimConfig'
  },
  {
    key: 'fullScreen',
    class: 'fullScreen',
    src: '/image/full_screen_icon.png',
    title: isFullscreen.value ? '退出全屏' : '全屏',
    width: '34',
    disabled: false,
    action: 'fullScreen'
  },
  {
    key: 'panelVisible',
    class: 'panelVisibleIcon',
    src: '/image/panel_visibel_icon.png',
    title: showPanel.value ? '隐藏控制面板' : '显示控制面板',
    width: '34',
    disabled: false,
    action: 'panelVisible'
  },
  {
    key: 'taskControl',
    class: 'taskControlBtn',
    src: '/image/task_control_icon.png',
    title: '任务控制',
    width: '34',
    disabled: isControlButtonDisabled.value,
    action: 'taskControl'
  },
  {
    key: 'entityControl',
    class: 'entityControlBtn',
    src: '/image/entity_control_icon.png',
    title: '实体控制',
    width: '34',
    disabled: isControlButtonDisabled.value,
    action: 'entityControl'
  }
])

// 面板样式计算
const panelStyle = computed(() => {
  let right = '45px'
  if (
    store.state.app &&
    (store.state.app.effectBoxShow || store.state.app.EntityInfo)
  ) {
    right = '390px'
  }

  return {
    right: right
  }
})

// 假设cesiumManager是全局可用的
const cesiumManager = (window as any).cesiumManager;

// 处理函数实现
const handleCameraReset = () => {
  if (cesiumManager) {
    cesiumManager.resetCamera(props.maxDistance, props.center);
  } else {
    console.error('cesiumManager not available');
  }
};

const handleGlobalSimConfig = () => {
  store.commit('SET_GlobalEffectBoxShow', true);
};

const handleFullscreen = () => {
  if (cesiumManager) {
    isFullscreen.value = cesiumManager.toggleFullscreen('GEOVISContainer');
  } else {
    console.error('cesiumManager not available');
  }
};

const handleTaskControl = () => {
  store.commit('SET_TaskControlBoxShow', true);
};

const handleEntityControl = () => {
  store.commit('SET_EntityControlBoxShow', true);
};

// 按钮点击处理函数
const handleButtonClick = (button: any) => {
  if (button.disabled) {
    return;
  }

  switch (button.action) {
    case 'cameraReset':
      handleCameraReset();
      break;
    case 'globalSimConfig':
      handleGlobalSimConfig();
      break;
    case 'fullScreen':
      handleFullscreen();
      break;
    case 'panelVisible':
      showPanel.value = !showPanel.value;
      break;
    case 'taskControl':
      handleTaskControl();
      break;
    case 'entityControl':
      handleEntityControl();
      break;
    default:
      console.warn('Unknown button action:', button.action);
  }
}
</script>

<style lang="less" scoped>
.panel-controls {
  position: absolute;
  bottom: 91px;
  background-color: #002f49;
  border: 1px solid var(--app-border-color);
  border-radius: 4px;
  padding: 10px;
  z-index: 100;

  .left {
    .item {
      cursor: pointer;
      align-items: center;
      color: white;
      padding: 5px 10px;
      border-radius: 4px;
      transition: background-color 0.2s;

      &:hover {
        background-color: #004066;
      }

      .icon {
        margin-right: 5px;
        width: 18px;
        height: 18px;
        color: #469fdd;
      }
    }
  }
}

/* 控制按钮组样式 */
.cameraReset,
.globalSimConfig {
  position: absolute;
  right: 350px;
  bottom: 74px;
  z-index: 1;
  cursor: pointer;
}

.globalSimConfig {
  bottom: 36px;
}

.entityControlBtn {
  bottom: 195px;
  right: 350px;
  background: url('/image/entity_control_icon.png');
}

.fullScreen {
  position: absolute;
  right: 350px;
  bottom: 112px;
  z-index: 1;
  cursor: pointer;
}

.taskControlBtn {
  bottom: 155px;
  background: url('/image/task_icon.png');
  right: 384px;
}

.entityControlBtn,
.taskControlBtn {
  width: 34px;
  height: 34px;
  border: 0;
  position: absolute;
  z-index: 1;
  img {
    width: 36px;
    height: 34px;
  }
}

.panelVisibleIcon {
  bottom: 235px;
  position: absolute;
  z-index: 1;
}
</style>