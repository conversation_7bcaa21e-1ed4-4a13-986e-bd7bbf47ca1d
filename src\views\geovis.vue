<!--
 * @Author: 老范
 * @Date: 2025-03-20 17:50:45
 * @LastEditors: 老范
 * @LastEditTime: 2025-07-25 09:31:54
 * @Description: 请填写简介
-->
<template>
  <div id="GEOVISContainer">
    <div class="leftCardBox">
      <ScenarioInfo
        v-show="store.state.app.ifScenarioShow"
        :scenarioInfo="scenarioParams"
      />
      <LogCard
        v-show="store.state.app.ifLogBoxShow"
        ref="logBoxRef"
        @sendMessage="setLogInfo"
      />
    </div>
    <div class="rightCardBox">
      <EntityListCard
        v-show="store.state.app.entityListShow"
        @g-db-click="focusEntity"
        :entitiesList="existEntity"
      />
      <EntityInfoCard v-show="store.state.app.EntityInfo" />
      <EntityConfig v-show="store.state.app.effectBoxShow" />
    </div>
    <CardConfig />
    <PanelControls v-if="showPanelControls" :visible="showPanelControls" />
    <img
      class="cameraReset"
      src="/image/camera_reset_icon.png"
      alt=""
      title="重置相机"
      @click="handleCameraReset"
      :style="{ right: (store.state.app.effectBoxShow || store.state.app.EntityInfo) ? '350px' : '7px' }"
    />
    <img
      class="globalSimConfig"
      src="/image/global_sim_config.png"
      alt=""
      title="态势显示配置"
      @click="handleGlobalSimConfig"
      :style="{ right: (store.state.app.effectBoxShow || store.state.app.EntityInfo) ? '350px' : '7px' }"
    />
    <img
      class="fullScreen"
      src="/image/full_screen_icon.png"
      alt=""
      :title="isFullscreen ? '退出全屏' : '全屏'"
      width="34"
      @click="handleFullscreen"
      :style="{ right: (store.state.app.effectBoxShow || store.state.app.EntityInfo) ? '350px' : '7px' }"
    />
    <img
      class="panelVisibleIcon"
      width="34"
      src="/image/panel_visibel_icon.png"
      title="显示控制面板"
      @click="showPanelControls = !showPanelControls"
      :style="{ right: (store.state.app.effectBoxShow || store.state.app.EntityInfo) ? '350px' : '7px' }"
    />
    <el-button
      class="taskControlBtn"
      title="任务控制"
      :style="{ right: (store.state.app.effectBoxShow || store.state.app.EntityInfo) ? '350px' : '7px' }"
      :disabled="
        store.state.simulationConfig.taskStatus !== 2 ||
        store.state.simulationConfig.runMode === 1
      "
      @click="handleTaskControl"
    >
    </el-button>
    <el-button
      class="entityControlBtn"
      title="实体控制"
      :style="{ right: (store.state.app.effectBoxShow || store.state.app.EntityInfo) ? '350px' : '7px' }"
      :disabled="
        store.state.simulationConfig.taskStatus !== 2 ||
        store.state.simulationConfig.runMode === 1
      "
      @click="handleEntityControl"
    ></el-button>
    <GlobalEffectConfigDialog />
    <EntityControlDialog
      :entitiesList="existEntity"
      :entitiesTypeList="entitiesTypeList"
    />
    <TaskControlDialog :entitiesList="existEntity" />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, provide } from 'vue'
import { useStore } from 'vuex'
import EntityControlDialog from '@/components/ControlDialog/EntityControlDialog.vue'
import TaskControlDialog from '@/components/ControlDialog/TaskControlDialog.vue'
import GlobalEffectConfigDialog from '@/components/ControlDialog/GlobalEffectConfigDialog.vue'
import ScenarioInfo from '@/components/SimCard/ScenarioInfo.vue'
import LogCard from '@/components/SimCard/LogCard.vue'
import EntityListCard from '@/components/SimCard/EntityListCard.vue'
import EntityInfoCard from '@/components/SimCard/EntityInfoCard.vue'
import EntityConfig from '@/components/SimCard/EntityConfig.vue'
import CardConfig from '@/components/SimCard/CardConfig.vue'
import PanelControls from '@/components/SimCard/PanelControls.vue'
import { ModelManager } from '@/utils/modelControler'
import { EffectManager } from '@/utils/effectManager'
import { ModelBubbleManager } from '@/utils/ModelBubbleManager'

const GV = window.GV
const store = useStore()

const scenarioParams = reactive({
  scenarioDescribe: '',
  scenarioName: '',
  modeText: '',
  startTime: '',
  realTime: 0,
})
const createView = () => {
  const viewer = new GV.GeoCanvas('GEOVISContainer', {
    baseLayerPicker: false,
    shouldAnimate: true, //动画开启
  })
  viewer.imageryLayers.addImageryProvider(
    new Cesium.UrlTemplateImageryProvider({
      url: `${window.GVJ.URLS.mapUrl}`,
      minimumLevel: 0,
      maximumLevel: 8,
    })
  )
  return viewer
}
const isFullscreen = ref(false)
const showPanelControls = ref(false)
const currentEntityId = ref('')
const currentSide = ref('red')
/** 共享当前选中的实体id */
provide('currentEntityId', currentEntityId)
provide('currentSide', currentSide)
const existEntity = ref([])
const entitiesTypeList = ref([])
const logBoxRef = ref()
const center = ref([])
const maxDistance = ref(0)
/** 重置相机 */
const handleCameraReset = () => {
  // 北京坐标: 经度 116.4074, 纬度 39.9042
  // 高度设置为 20000000 米 (2万公里) 以便能看到整个地球
  window.viewer.iCamera.flyTo(
    new GV.ViewPoint(116.4074, 39.9042, 20000000, 0, -90, 0),
    0 // 飞行时间
  )
  viewerToAnodic(maxDistance.value, center.value)
  window.viewer.trackedEntity = null
}

/** 态势显示配置 */
const handleGlobalSimConfig = () => {
  store.commit('SET_GlobalEffectBoxShow', true)
}

/** 实体控制 */
const handleEntityControl = () => {
  store.commit('SET_EntityControlBoxShow', true)
}

/** 任务控制 */
const handleTaskControl = () => {
  store.commit('SET_TaskControlBoxShow', true)
}

/** 跟踪实体 */
const focusEntity = (entityId: string) => {
  window.viewer.trackedEntity = window.viewer.entities.getById(entityId)
}

/** 追加日志 */
const setLogInfo = (message: string) => {
  logBoxRef.value.sendMessage(message)
}
const setEntityList = (list: string) => {
  existEntity.value = list
}

/** 相机飞行至场景上空 */
const viewerToAnodic = (maxDistance: number, center: number[]) => {
  const scale = (maxDistance * 1000) / 2
  const earthRadius = 6371000
  const alt =
    Math.sqrt(3) * scale +
    Math.sqrt(earthRadius * earthRadius - scale * scale) -
    earthRadius

  // 动态偏移高度，例如增加场景 scale 的 2.5倍
  const offset = scale * 2.5

  console.log('🚀 ~ viewerToAnodic ~ center:', center)
  window.viewer.iCamera.flyTo(new GV.ViewPoint(...center, alt + offset), 0)
}

/** 全屏 */
const handleFullscreen = () => {
  const dom = document.getElementById('GEOVISContainer')
  if (!dom) return
  if (!document.fullscreenElement) dom.requestFullscreen()
  else document.exitFullscreen()
  isFullscreen.value = !document.fullscreenElement
}
// 声明WebSocket引用变量
let wsModelData: WebSocket | null = null
let wsSystemInfo: WebSocket | null = null
let wsRealtimeData: WebSocket | null = null
let wsModelState: WebSocket | null = null
let wsPlatformType: WebSocket | null = null
let routeLines = new Map()

const initWebsocket = () => {
  const effectManager = new EffectManager(
    window.viewer,
    `${(window as any).GVJ.URLS.socketServer}api/v1/data-socket/getEvents`,
    setLogInfo,
    setEntityList,
    existEntity.value
  )
  window.effectManager = effectManager
  const modelManager = new ModelManager(window.viewer, effectManager, {
    maxTrailPoints: 200,
    trailColor: Cesium.Color.RED,
  })
  window.modelManager = modelManager

  // 初始化各个WebSocket连接
  initSystemInfoSocket()
  initModelDataSocket()
  initRealtimeDataSocket()
  initPlatformTypeSocket()
}

// 初始化模型数据WebSocket - 获取初始模型和路线数据
const initModelDataSocket = () => {
  wsModelData = new WebSocket(
    `${
      (window as any).GVJ.URLS.socketServer
    }api/v1/data-socket/getModelData?pushType=init`
  )

  wsModelData.onmessage = event => {
    const data: any = JSON.parse(event.data)
    console.log(data, '初始模型和路线数据')

    scenarioParams.scenarioDescribe = data.description
    scenarioParams.scenarioName = data.name
    scenarioParams.startTime = data.startTime
    // 初始化模型
    data.init_model.forEach(item => {
      const model = existEntity.value.find(
        existItem => existItem.na === item.na
      )
      if (model) return
      existEntity.value.push(item)
      window.modelManager.createNewModel(item)
      window.modelManager.trailPrimitive.addTrail(
        item.na,
        [
          { lo: item.lo, la: item.la, al: item.al | 0 },
          { lo: item.lo, la: item.la, al: item.al | 0 },
        ],
        { si: item.si }
      )
    })

    if (data.routes.length > 0) {
      // 清除路线
      routeLines.forEach((line, id) => {
        window.viewer.entities.remove(line)
      })
      routeLines.clear()
    }
    // 预设航路
    data.routes.forEach(route => {
      const id = `${route.type}_initRoute`
      if (!routeLines.get(id)) {
        const routeAry = route.parameters.flat()
        const catrePosition =
          Cesium.Cartesian3.fromDegreesArrayHeights(routeAry)

        const spline = new Cesium.CatmullRomSpline({
          times: catrePosition.map(
            (_, index) => index / (catrePosition.length - 1)
          ),
          points: catrePosition,
        })
        const smoothPosition: Cesium.Cartesian3[] = []
        const sampleCount = 10
        for (let index = 0; index < sampleCount; index++) {
          const time = index / sampleCount
          smoothPosition.push(spline.evaluate(time))
        }
        const line = window.viewer.entities.add({
          id,
          name: '线',
          polyline: {
            width: 1.0,
            positions: catrePosition,
            material: Cesium.Color.YELLOW.withAlpha(0.7),
          },
        })
        routeLines.set(id, line)
      }
    })
    maxDistance.value = data.cameraConfig.maxDistance
    center.value = data.cameraConfig.center
    if (store.state.simulationConfig.taskStatus !== 2) {
      // 不是运行状态
      viewerToAnodic(maxDistance.value, center.value)
    }
  }
}

// 初始化系统信息WebSocket - 获取系统状态信息
const initSystemInfoSocket = () => {
  let isMounted = true
  wsSystemInfo = new WebSocket(
    `${
      (window as any).GVJ.URLS.socketServer
    }api/v1/data-socket/getSysInfo?type=init`
  )

  wsSystemInfo.onmessage = event => {
    const data: any = JSON.parse(event.data)
    console.log(data, '系统状态信息')
    scenarioParams.modeText = `${data.mode ? '回放' : '推演'}模式-${
      data.simulateMode ? '帧' : '事件'
    }模式`
    store.commit('SET_RUNMODE_STATUS', data.mode)
    // 更新全局taskStatus变量
    store.commit('SET_TASK_STATUS', data.taskStatus)
    if (data.taskStatus === 0) {
      // 清空
      window.modelManager.reset()
      scenarioParams.realTime = 0
      logBoxRef.value.clearList()
      existEntity.value.length = 0
      currentEntityId.value = ''
      entitiesTypeList.value = []
      routeLines.forEach((line, id) => {
        window.viewer.entities.remove(line)
      })
      routeLines.clear()
    } else if (data.taskStatus === 4) {
      // existEntity.value.length = 0
      // logBoxRef.value.clearList()
      // currentEntityId.value = ''
    } else if (data.taskStatus === 6) {
      // 全部重置
      window.modelManager.reset() // 清空
      // 清除路线
      routeLines.forEach((line, id) => {
        window.viewer.entities.remove(line)
      })
      routeLines.clear()
      existEntity.value.length = 0
      logBoxRef.value.clearList()
      currentEntityId.value = ''
    }
    if (
      data.taskStatus === 1 ||
      ([2, 3].includes(data.taskStatus) && isMounted)
    ) {
      // viewerToAnodic(maxDistance.value, center.value)
      isMounted = false
    }
  }
}

// 初始化实时数据WebSocket - 获取模型实时位置更新
const initRealtimeDataSocket = () => {
  wsRealtimeData = new WebSocket(
    `${
      (window as any).GVJ.URLS.socketServer
    }api/v1/data-socket/getModelData?pushType=realtime`
  )

  wsRealtimeData.onmessage = event => {
    const data: any[] = JSON.parse(event.data)
    //console.log(data,'模型实时位置更新');

    window.modelManager.batchUpdate(data.up, data.ts)
    scenarioParams.realTime = data.ts
    // 新增：同步simTimestamp到vuex
    store.commit('SET_SIM_TIMESTAMP', data.ts)
  }
}

// 初始化平台类型WebSocket - 获取实体类型信息
const initPlatformTypeSocket = () => {
  wsPlatformType = new WebSocket(
    `${
      (window as any).GVJ.URLS.socketServer
    }api/v1/data-socket/getModelData?pushType=platformType`
  )

  wsPlatformType.onmessage = event => {
    const data: any = JSON.parse(event.data)
    console.log(data, '实体类型信息')
    data.forEach(i => {
      const blueType = Object.keys(i.blueSide.types)
      const redType = Object.keys(i.redSide.types)
      const typeSet = new Set([
        ...entitiesTypeList.value,
        ...blueType,
        ...redType,
      ])
      entitiesTypeList.value = Array.from(typeSet)
    })
  }
}

// 关闭所有WebSocket连接
const closeAllWebSockets = () => {
  ;[
    wsModelData,
    wsSystemInfo,
    wsRealtimeData,
    wsModelState,
    wsPlatformType,
  ].forEach(ws => {
    if (ws && ws.readyState !== WebSocket.CLOSED) {
      ws.close()
    }
  })

  // 重置引用
  wsModelData = null
  wsSystemInfo = null
  wsRealtimeData = null
  wsModelState = null
  wsPlatformType = null
}

// 组件卸载时关闭所有WebSocket连接
onUnmounted(() => {
  closeAllWebSockets()
})
onMounted(async () => {
  window.viewer = createView()
  const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas)
  // 双击
  handler.setInputAction(function (
    movement: Cesium.PositionedEvent | Cesium.MoveEvent | Cesium.Touch2Event
  ) {
    const pickedObjecy = window.viewer.scene.pick(movement.position)
    if (!pickedObjecy) return
    currentEntityId.value = pickedObjecy.id.id
    window.viewer.trackedEntity = window.viewer.entities.getById(
      pickedObjecy.id.id
    )
  },
  Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK)
  // 单击
  handler.setInputAction(function (
    movement: Cesium.PositionedEvent | Cesium.MoveEvent | Cesium.Touch2Event
  ) {
    const pickedObjecy = window.viewer.scene.pick(movement.position)
    if (!pickedObjecy) return
    currentEntityId.value = pickedObjecy.id.id
    const modelBubbleManager = new ModelBubbleManager()
    // modelBubbleManager.showBubble(pickedObjecy.id.id)
  },
  Cesium.ScreenSpaceEventType.LEFT_CLICK)
  // 开启帧率
  // window.viewer.scene.debugShowFramesPerSecond = true
  initWebsocket()
})
</script>

<style lang="less" scoped>
#GEOVISContainer {
  width: 100%;
  height: 100%;
  position: absolute;
  .leftCardBox,
  .rightCardBox {
    padding-top: 5px;
    position: absolute;
    height: calc(100% - 36px);
    top: 0;
    z-index: 1;
    font-size: 16px;
  }
  .leftCardBox {
    width: 336px;
    left: 15px;
  }
  .rightCardBox {
    right: 15px;
    width: 331px;
  }
  .cameraReset,
  .globalSimConfig {
    position: absolute;
    right: 350px;
    bottom: 74px;
    z-index: 1;
    cursor: pointer;
  }
  .globalSimConfig {
    bottom: 36px;
  }
  .entityControlBtn {
    bottom: 195px;
    right: 350px;
    background: url('/image/entity_control_icon.png');
  }
  .fullScreen {
    position: absolute;
    right: 350px;
    bottom: 112px;
    z-index: 1;
    cursor: pointer;
  }
  .taskControlBtn {
    bottom: 155px;
    background: url('/image/task_icon.png');
    right: 384px;
  }
  .entityControlBtn,
  .taskControlBtn {
    width: 34px;
    height: 34px;
    border: 0;
    position: absolute;
    z-index: 1;
    img {
      width: 36px;
      height: 34px;
    }
  }
  .panelVisibleIcon {
    bottom: 235px;
    position: absolute;
    z-index: 1;
  }
}
</style>
