<template>
  <div
    class="logBox"
    @mouseleave="dataMove = true"
    @mousemove="dataMove = false"
  >
    <el-row class="title">
      <span>日志</span>
    </el-row>
    <div class="searchBox">
      <el-input
        v-model="value"
        style="width: 100%"
        placeholder="请输入关键字"
        size="small"
        :suffix-icon="Search"
      />
    </div>
    <div class="timeLineBox" v-if="list.length">
      <el-timeline direction="vertical" ref="logBox">
        <el-timeline-item
          v-for="item in list"
          :key="item.sendTime"
          :timestamp="item.sendTime"
          placement="top"
        >
          <!-- :timestamp="dayjs(item.sendTime).format('YYYY-MM-DD HH:mm:ss')" -->
          <!-- {{ item.messages }} -->
          <div class="logInfoBox">
            {{ item.messages }}
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>
    <el-row v-else class="emptyBox">
      <Empty />
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch } from 'vue'
import { Search } from '@element-plus/icons'
import Empty from '@/components/Empty.vue'
const value = ref('')
const logBox = ref()
let dataMove = ref(false) // 鼠标是否移入
const fullList = ref<Array<{ messages: string; sendTime: string }>>([]) // 原始日志
const list = ref<Array<{ messages: string; sendTime: string }>>([]) // 当前展示日志
const sendMessage = (val: any) => {
  val.data.forEach((item: any) => {
    fullList.value.push({
      messages: item.platform_name + ' ' + item.comment,
      sendTime: val.time + 's',
    })
  })
  filterList() // 每次添加后自动过滤
  // 更新数据时, 如果鼠标不在日志区域, 则追踪至列表最下方
  if (!dataMove.value && logBox.value) {
    nextTick(() => {
      logBox.value.$el.scrollTop = logBox.value.$el.scrollHeight
    })
  }
}
// 搜索过滤
const filterList = () => {
  const keyword = value.value.trim().toLowerCase()
  if (!keyword) {
    list.value = [...fullList.value]
  } else {
    list.value = fullList.value.filter(item =>
      item.messages.toLowerCase().includes(keyword)
    )
  }
}

// 清除日志
const clearList = () => {
  fullList.value = []
  list.value = []
}

// 搜索框监听输入变化
watch(value, filterList)
defineExpose({ clearList, sendMessage })
</script>
<style lang="less" scoped>
.logBox {
  height: calc(100% - 332px - 8px);
  position: absolute;
  width: 100%;
  top: calc(332px + 8px);
  background: url('/image/box_bg.png') no-repeat 0px 37px;
  background-size: 100% 100%;
  .title {
    height: 37px;
    font-size: 16px;
    padding-bottom: 4px;
    font-family: Bold;
    font-style: oblique;
    align-items: center;
    padding-left: 35px;
    background: url('/image/title_bg.png') no-repeat;
    span {
      background: url('/image/title_font_bg.png') no-repeat 15px 5px;
    }
  }
  .searchBox {
    padding: 5px;
  }
  .timeLineBox {
    padding: 0 18px;
    height: calc(100% - 32px - 46px);
    :deep(.el-timeline) {
      height: 100%;
      overflow: auto;

      .el-timeline-item__node--normal {
        left: 0px;
      }
      .el-timeline-item__tail {
        border-left: 2px solid #507b85;
        left: 5px;
      }
      .el-timeline-item__node {
        border: 2px solid #959595;
      }
    }
  }
  .emptyBox {
    padding-top: 50px;
    flex-direction: column;
    align-items: center;
  }
}
</style>
