import {
  Store as VuexStore,
  CommitOptions,
  Module
} from 'vuex'
import { RootState } from '../../../store/index'
import { state } from './state'
import { mutations, Mutations } from './mutations'
import type { SimulationConfigState } from './state'

export { SimulationConfigState }

export type SimulationConfigStore<S=SimulationConfigState> = Omit<VuexStore<S>,'getters'|'commit'>
&{
  commit<K extends keyof Mutations, P extends Parameters<Mutations[K]>[1]>(
    key: K,
    payload: P,
    options?: CommitOptions
  ): ReturnType<Mutations[K]>
}

export const store: Module<SimulationConfigState, RootState> = {
  state,
  mutations
}