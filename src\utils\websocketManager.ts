import { EffectManager } from './effectManager'
import { ModelManager } from './modelControler'

export interface WebSocketHandlers {
  onMessage?: (data: any) => void
  onError?: (event: Event) => void
  onOpen?: (event: Event) => void
  onClose?: (event: CloseEvent) => void
}

export class WebSocketManager {
  private connections: Map<string, WebSocket> = new Map()
  private modelManager: ModelManager | null = null
  private effectManager: EffectManager | null = null
  private setLogInfo: ((message: string) => void) | null = null
  private setEntityList: ((list: any[]) => void) | null = null
  private existEntity: any[] = []

  private socketServer: string = ''

  constructor(socketServer: string) {
    this.socketServer = socketServer
  }

  public setModelManager(modelManager: ModelManager) {
    this.modelManager = modelManager
  }

  public setEffectManager(effectManager: EffectManager) {
    this.effectManager = effectManager
  }

  public setLogFunctions(
    setLogInfo: (message: string) => void,
    setEntityList: (list: any[]) => void
  ) {
    this.setLogInfo = setLogInfo
    this.setEntityList = setEntityList
  }

  public setExistEntity(existEntity: any[]) {
    this.existEntity = existEntity
  }

  public initAllWebSockets() {
    this.initModelDataSocket()
    this.initSystemInfoSocket()
    this.initRealtimeDataSocket()
    this.initPlatformTypeSocket()
  }

  // 初始化模型数据WebSocket - 获取初始模型和路线数据
  private initModelDataSocket() {
    const ws = new WebSocket(
      `${this.socketServer}api/v1/data-socket/getModelData?pushType=init`
    )

    ws.onmessage = (event) => {
      const data: any = JSON.parse(event.data)
      console.log(data, '初始模型和路线数据')
      // 将数据传递回geovis.vue组件处理
      window.dispatchEvent(new CustomEvent('modelDataReceived', { detail: data }))
    }

    this.connections.set('modelData', ws)
  }

  // 初始化系统信息WebSocket - 获取系统状态信息
  private initSystemInfoSocket() {
    const ws = new WebSocket(
      `${this.socketServer}api/v1/data-socket/getSysInfo?type=init`
    )

    ws.onmessage = (event) => {
      const data: any = JSON.parse(event.data)
      console.log(data, '系统状态信息')

      // 将数据传递回geovis.vue组件处理
      window.dispatchEvent(new CustomEvent('systemInfoReceived', { detail: data }))
    }

    this.connections.set('systemInfo', ws)
  }

  // 初始化实时数据WebSocket - 获取模型实时位置更新
  private initRealtimeDataSocket() {
    const ws = new WebSocket(
      `${this.socketServer}api/v1/data-socket/getModelData?pushType=realtime`
    )

    ws.onmessage = (event) => {
      const data: any = JSON.parse(event.data)
      //console.log(data,'模型实时位置更新');

      // 将数据传递回geovis.vue组件处理
      window.dispatchEvent(new CustomEvent('realtimeDataReceived', { detail: data }))
    }

    this.connections.set('realtimeData', ws)
  }

  // 初始化平台类型WebSocket - 获取实体类型信息
  private initPlatformTypeSocket() {
    const ws = new WebSocket(
      `${this.socketServer}api/v1/data-socket/getModelData?pushType=platformType`
    )

    ws.onmessage = (event) => {
      const data: any = JSON.parse(event.data)
      console.log(data, '实体类型信息')

      // 将数据传递回geovis.vue组件处理
      window.dispatchEvent(new CustomEvent('platformTypeReceived', { detail: data }))
    }

    this.connections.set('platformType', ws)
  }


  public closeAllWebSockets() {
    this.connections.forEach((ws, key) => {
      if (ws && ws.readyState !== WebSocket.CLOSED) {
        ws.close()
      }
    })

    this.connections.clear()
  }
}