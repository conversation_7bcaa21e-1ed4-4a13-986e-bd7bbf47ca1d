/*
 * @Author: 老范
 * @Date: 2025-07-22 10:34:54
 * @LastEditors: 老范
 * @LastEditTime: 2025-07-24 10:23:48
 * @Description: 请填写简介
 */
export type EffectCheckState = 'checked' | 'unchecked' | 'indeterminate'

export interface SimulationConfigState {
  taskStatus: number
  simTimestamp: number
  redState: EffectCheckState
  blueState: EffectCheckState
  labelState: EffectCheckState
  communicationState: EffectCheckState
  routeState: EffectCheckState
  probState: EffectCheckState
  fireState: EffectCheckState
  trailState: EffectCheckState
  sensorState: EffectCheckState
  entityConfigCommunication: boolean
  entityConfigProb: boolean
  entityConfigFire: boolean
  entityConfigTrail: boolean
  entityConfigLabel: boolean
  entityConfigSensor: boolean
  runMode: number
}

export const state: SimulationConfigState = {
  taskStatus: 0,
  runMode: 0,
  simTimestamp: 0,
  redState: 'checked',
  blueState: 'checked',
  labelState: 'checked',
  communicationState: 'checked',
  routeState: 'checked',
  probState: 'checked',
  fireState: 'checked',
  trailState: 'checked',
  sensorState: 'checked',
  entityConfigCommunication: true,
  entityConfigProb: true,
  entityConfigFire: true,
  entityConfigTrail: true,
  entityConfigLabel: true,
  entityConfigSensor: true,
}
