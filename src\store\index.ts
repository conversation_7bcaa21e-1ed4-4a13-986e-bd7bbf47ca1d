
import { createStore } from 'vuex'
import { store as user, UserStore, UserState } from './modules/user/index'
import { store as app, AppStore, AppState } from './modules/app/index'
import { store as simulationConfig, SimulationConfigStore, SimulationConfigState } from './modules/simulationConfig/index'

export interface RootState {
  user: UserState,
  app: AppState,
  simulationConfig: SimulationConfigState
}

export type Store = UserStore<Pick<RootState, 'user'>> & 
                    AppStore<Pick<RootState, 'app'>> &
                    SimulationConfigStore<Pick<RootState, 'simulationConfig'>>

export const store = createStore({
  modules: {
    user,
    app,
    simulationConfig
  }
})

export function useStore(): Store {
  return store as Store
}
