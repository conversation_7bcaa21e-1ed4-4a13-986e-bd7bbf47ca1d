<template>
  <div class="entityInfoBox">
    <el-row class="title">
      <span>实体信息</span>
    </el-row>
    <div class="contentBox">
      <el-row class="item">
        <el-row class="label">
          <div class="iconBox">
            <img class="icon" src="/image/na_icon.png" alt="" />
          </div>

          <span>名称：</span>
        </el-row>
        <div class="value" :title="info.na">{{ info.na || '-' }}</div>
      </el-row>
      <!-- <el-row class="item">
        <el-row class="label">
          <div class="iconBox">
            <img class="icon" src="/image/si_icon.png" alt="" />
          </div>
          <span>阵营：</span>
        </el-row>
        <div class="value">{{ getSide }}</div>
      </el-row> -->
      <el-row class="item">
        <el-row class="label">
          <div class="iconBox">
            <img class="icon" src="/image/lon_icon.png" alt="" />
          </div>
          <span>经度：</span>
        </el-row>
        <div class="value">
          {{ info.lo !== undefined ? info.lo.toFixed(5) + '(度)' : '-' }}
        </div>
        <img
          class="icon"
          v-if="info.lo !== undefined"
          title="时间变化曲线图"
          src="/image/curve_icon.png"
          alt=""
          @click="
            () => {
              chartType = 'lo'
              showChartDialog = true
            }
          "
        />
      </el-row>
      <el-row class="item">
        <el-row class="label">
          <div class="iconBox">
            <img class="icon" src="/image/lat_icon.png" alt="" />
          </div>
          <span>纬度：</span>
        </el-row>
        <div class="value">
          {{ info.la !== undefined ? info.la.toFixed(5) + '(度)' : '-' }}
        </div>
        <img
          class="icon"
          v-if="info.la !== undefined"
          title="时间变化曲线图"
          src="/image/curve_icon.png"
          alt=""
          @click="
            () => {
              chartType = 'la'
              showChartDialog = true
            }
          "
        />
      </el-row>
      <el-row class="item">
        <el-row class="label">
          <div class="iconBox">
            <img class="icon" src="/image/alt_icon.png" alt="" />
          </div>
          <span>高度：</span>
        </el-row>
        <div class="value">
          {{ info.al !== undefined ? info.al.toFixed(2) + '(米)' : '-' }}
        </div>
        <img
          class="icon"
          v-if="info.al !== undefined"
          title="时间变化曲线图"
          src="/image/curve_icon.png"
          alt=""
          @click="
            () => {
              chartType = 'al'
              showChartDialog = true
            }
          "
        />
      </el-row>
      <el-row class="item">
        <el-row class="label">
          <div class="iconBox">
            <img class="icon" src="/image/pi_icon.png" alt="" />
          </div>
          <span>俯仰：</span>
        </el-row>
        <div class="value">
          {{ info.pi !== undefined ? info.pi.toFixed(2) + '(度)' : '-' }}
        </div>
        <img
          class="icon"
          v-if="info.pi !== undefined"
          title="时间变化曲线图"
          src="/image/curve_icon.png"
          alt=""
          @click="
            () => {
              chartType = 'pi'
              showChartDialog = true
            }
          "
        />
      </el-row>
      <el-row class="item">
        <el-row class="label">
          <div class="iconBox">
            <img class="icon" src="/image/ya_icon.png" alt="" />
          </div>
          <span>偏航：</span>
        </el-row>
        <div class="value">
          {{ info.ya !== undefined ? info.ya.toFixed(2) + '(度)' : '-' }}
        </div>
        <img
          class="icon"
          v-if="info.ya !== undefined"
          title="时间变化曲线图"
          src="/image/curve_icon.png"
          alt=""
          @click="
            () => {
              chartType = 'ya'
              showChartDialog = true
            }
          "
        />
      </el-row>
      <el-row class="item">
        <el-row class="label">
          <div class="iconBox">
            <img class="icon" src="/image/ro_icon.png" alt="" />
          </div>
          <span>翻滚：</span>
        </el-row>
        <div class="value">
          {{ info.ro !== undefined ? info.ro.toFixed(2) + '(度)' : '-' }}
        </div>
        <img
          class="icon"
          v-if="info.ro !== undefined"
          title="时间变化曲线图"
          src="/image/curve_icon.png"
          alt=""
          @click="
            () => {
              chartType = 'ro'
              showChartDialog = true
            }
          "
        />
      </el-row>
      <el-row class="item">
        <el-row class="label">
          <div class="iconBox">
            <img class="icon" src="/image/sp_icon.png" alt="" />
          </div>
          <span>速度：</span>
        </el-row>
        <div class="value">
          {{ info.sp !== undefined ? info.sp.toFixed(2) + '(米/秒)' : '-' }}
        </div>
        <img
          class="icon"
          v-if="info.sp !== undefined"
          title="时间变化曲线图"
          src="/image/curve_icon.png"
          alt=""
          @click="
            () => {
              chartType = 'sp'
              showChartDialog = true
            }
          "
        />
      </el-row>
    </div>
    <ModelPropertyChartDialog
      :id="currentEntityId"
      :type="chartType"
      v-model:visible="showChartDialog"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, watch, computed, inject } from 'vue'
import { useStore } from 'vuex'
import ModelPropertyChartDialog from './ModelPropertyChartDialog.vue'
interface InfoType {
  la: number // 纬度
  lo: number // 经度
  al: number // 高度
  sp: number // 速度
  na: string
  // 模型状态: string
  si: string
  pi: number // 俯仰
  ya: number // 偏航
  ro: number // 翻滚
}
const currentEntityId = inject('currentEntityId', ref(''))
const info = ref({} as InfoType)
let frameId: number

const MAX_UPDATE_RETRY = 3

const store = useStore()
const showChartDialog = ref(false)
const chartType = ref('lo')

const getSide = computed(() => {
  let si = '-'
  switch (info.value.si) {
    case 'red':
      si = '红'
      break
    case 'blue':
      si = '蓝'
      break
    default:
      break
  }
  return si
})

const update = (retry = 0) => {
  if (!window.viewer) return
  const entity = window.viewer.entities.getById(currentEntityId.value)
  const clickEntity = entity && entity.properties
  if (!clickEntity) {
    if (retry < MAX_UPDATE_RETRY) {
      setTimeout(() => update(retry + 1), 200)
    }
    return
  }
  info.value = {
    la: clickEntity.la.getValue(),
    lo: clickEntity.lo.getValue(),
    al: clickEntity.al.getValue(),
    sp: clickEntity.sp.getValue(),
    na: clickEntity.na.getValue(),
    // 模型状态:  clickEntity.na.getValue(),
    si: clickEntity.si.getValue(),
    pi: clickEntity.pi.getValue(),
    ya: clickEntity.ya.getValue(),
    ro: clickEntity.ro.getValue(),
  }
  frameId = requestAnimationFrame(() => update())
}
watch(
  () => currentEntityId.value,
  newId => {
    if (newId) {
      cancelAnimationFrame(frameId)
      update()
    } else {
      info.value = {} as InfoType
    }
  },
  {
    immediate: true,
  }
)
watch(
  () => store.state.simulationConfig.taskStatus,
  val => {
    if (val === 4) {
      showChartDialog.value = false
    }
    if (val !== 2 && val !== 4) cancelAnimationFrame(frameId)
    else update()
  }
)
onMounted(() => {
  update()
})
</script>

<style lang="less" scoped>
.entityInfoBox {
  height: 300px;
  position: absolute;
  width: 100%;
  bottom: 200px;
  background: url('/image/box_bg.png') no-repeat 0px 37px;
  background-size: 100% 100%;
  .title {
    height: 37px;
    font-size: 16px;
    font-family: Bold;
    font-style: oblique;
    align-items: center;
    padding-left: 35px;
    padding-bottom: 4px;
    background: url('/image/title_bg.png') no-repeat;
    span {
      background: url('/image/title_font_bg.png') no-repeat 15px 5px;
    }
  }
  .contentBox {
    height: calc(100% - 32px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 5px 15px;
    .item {
      display: flex;
      align-items: center;
      height: 26px;
      .label {
        display: flex;
        align-items: center;
        flex-shrink: 0;
        min-width: 80px;
        .iconBox {
          width: 25px;
          text-align: left;
          margin-top: -2px;
        }
      }
      .value {
        flex: 1;
        min-width: 0;
        font-weight: 600;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .icon {
        cursor: pointer;
      }
    }
  }
}
</style>
