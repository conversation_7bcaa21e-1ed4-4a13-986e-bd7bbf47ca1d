
:root{
  --app-border-color: #2ca1e3;
}
@font-face {
  font-family: Bold;
  src: url('../assets/fonts/SourceHanSansCN-Bold.ttf');
}

@font-face {
  font-family: Medium;
  src: url('../assets/fonts/SourceHanSansCN-Medium.ttf');
}
@font-face {
  font-family: Regular;
  src: url('../assets/fonts/SourceHanSansCN-Regular.ttf');
}
.el-input {
  --el-input-background-color: transparent;
  --el-component-size-large: 40px;
  --el-component-size-small: 26px;
  --el-border-color: var(--app-border-color);
  --el-input-border-radius: 0;
  --el-disabled-border-color: var(--el-border-color);
  --el-input-font-color: #fff;
  --el-input-bg-color: transparent;
  --el-fill-color-light: transparent;
  --el-input-border: 1px solid var(--app-border-color);
  --el-input-hover-border: var(--app-border-color);
  --el-input-text-color: #fff;
  background: url('/image/search_inp_bg.png')!important;
  background-size: 100% 100% !important;
  .el-input__inner {
    font-size: 14px;
  }
}
.el-input.is-disabled .el-input__wrapper,
.el-textarea.is-disabled .el-textarea__inner {
  background-image: linear-gradient(0deg, #4e6f82 -20%, #05344e 100%);
}
.el-textarea {
  --el-input-bg-color: transparent;
  --el-input-border-color: #4a86d2;
  --el-input-focus-border-color: #409eff;
  --el-disabled-border-color: var(--el-input-border-color);
}

.el-input-number {
  --el-fill-color-light: transparent;
  --el-border-radius-base: 0;
  --el-border: 1px solid var(--app-border-color);
}

.el-button {
  --el-button-bg-color: #125d8d;
  --el-button-hover-bg-color: #125d8d;
  --el-button-hover-text-color: #fff;
  --el-border-radius-base: 0;
  --el-font-size-base: 15px;
  --el-border-color: #36b6ff;
  --el-button-text-color: #fff;
  --el-button-disabled-bg-color: #125d8d;
  --el-button-disabled-border-color: var(--app-border-color)
  height: 30px;
}

.el-dialog {
  --el-dialog-bg-color: transparent;
  --el-dialog-padding-primary: 0;
  --el-dialog-header-bg: #12577f;
  --el-dialog-border-color: #36b6ff;
  --el-text-color-regular: #fff;
  --el-text-color-primary: #fff;
  --el-dialog-content-font-size: 15px;
  border: 0;
  font-family: Avenir, Helvetica, Arial, sans-serif;
  background: url('/image/box_bg.png') no-repeat 0px 37px;
  background-size: 100% 100%;
  .el-dialog__header {
    height: 32px;
    line-height: 34px;
    text-align: center;
    background: url('/image/title_bg.png') no-repeat;
  background-size: 100% 100%;

    .el-dialog__headerbtn{
      height: 38px;
    }
  }
  .el-dialog__body {
    padding: 15px 10px 0;
  }
  .el-dialog__footer {
    margin-bottom: 15px;
    text-align: center;
  }
}

.el-select {
  --el-border-color: var(--app-border-color);
  --el-border-radius-base: 0;
  .el-select__wrapper {
    background-image: linear-gradient(0deg, #0c5e8e 0%, #00456d 75%);
    .el-select__tags-text {
      font-size: 14px;
    }
  }
}
.el-select__popper {
  --el-bg-color-overlay: #00598c;
  --el-text-color-regular: #fff;
  --el-fill-color-light: #0a79b8;
  --el-datepicker-border-color: var(--app-border-color);
}
.el-popper {
  --el-border-color-light: var(--app-border-color);
}

.el-message-box {
  --el-bg-color: #022e48;
  --el-messagebox-title-color: #fff;
  --el-messagebox-content-color: #fff;
  border: 1px solid var(--app-border-color);
}
.el-timeline{
  --el-text-color-primary: #fff;
  --el-text-color-secondary: #00a4c5;
}