<template>
  <div class="panel-controls" v-if="visible" :style="panelStyle">
    <div class="left">
      <el-row
        class="item"
        @click="
          store.commit('SET_ScenarioShow', !store.state.app.ifScenarioShow)
        "
      >
        <el-icon class="icon"
          ><View v-if="store.state.app.ifScenarioShow" /><Hide v-else
        /></el-icon>
        方案信息
      </el-row>
      <el-row
        class="item"
        @click="store.commit('SET_LogBoxShow', !store.state.app.ifLogBoxShow)"
      >
        <el-icon class="icon"
          ><View v-if="store.state.app.ifLogBoxShow" /><Hide v-else
        /></el-icon>
        日志
      </el-row>
      <el-row
        class="item"
        @click="
          store.commit('SET_EntityListBoxShow', !store.state.app.entityListShow)
        "
      >
        <el-icon class="icon"
          ><View v-if="store.state.app.entityListShow" /><Hide v-else
        /></el-icon>
        实体列表
      </el-row>
      <el-row
        class="item"
        @click="store.commit('SET_EntityInfoShow', !store.state.app.EntityInfo)"
      >
        <el-icon class="icon"
          ><View v-if="store.state.app.EntityInfo" /><Hide v-else
        /></el-icon>
        实体信息
      </el-row>
      <el-row
        class="item"
        @click="
          store.commit('SET_EffectBoxShow', !store.state.app.effectBoxShow)
        "
      >
        <el-icon class="icon"
          ><View v-if="store.state.app.effectBoxShow" /><Hide v-else
        /></el-icon>
        实体配置
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useStore } from 'vuex'
import { View, Hide } from '@element-plus/icons-vue'
import { computed } from 'vue'

defineProps<{
  visible: boolean
}>()

const store = useStore()

const panelStyle = computed(() => {
  let right = '45px'
  if (
    store.state.app &&
    (store.state.app.effectBoxShow || store.state.app.EntityInfo)
  ) {
    right = '390px'
  }

  return {
    right: right,
  }
})
</script>

<style lang="less" scoped>
.panel-controls {
  position: absolute;
  bottom: 91px;
  background-color: #002f49;
  border: 1px solid var(--app-border-color);
  border-radius: 4px;
  padding: 10px;
  z-index: 100;

  .left {
    .item {
      cursor: pointer;
      align-items: center;
      color: white;
      padding: 5px 10px;
      border-radius: 4px;
      transition: background-color 0.2s;

      &:hover {
        background-color: #004066;
      }

      .icon {
        margin-right: 5px;
        width: 18px;
        height: 18px;
        color: #469fdd;
      }
    }
  }
}
</style>
