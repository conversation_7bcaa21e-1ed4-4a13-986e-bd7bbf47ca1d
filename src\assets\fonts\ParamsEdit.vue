<!--
  模型以及组件参数编辑页面
 * @Author: liukun
 * @Date: 2024-04-09 11:22:01
 * @LastEditors: tzs
 * @LastEditTime: 2025-07-29 10:40:44
 * @FilePath: \model-controler\src\views\ParamsEdit.vue
-->
<script setup lang="ts">
import {ref, defineAsyncComponent, provide, watch, Ref, inject, onMounted, nextTick} from 'vue';
import cloneDeep from 'lodash/cloneDeep';
import isEmpty from 'lodash/isEmpty';
import {ElMessage, ElMessageBox} from 'element-plus';
import {Base64} from 'js-base64';
import {pinyin} from 'pinyin-pro';
import {getGroupsApi, getTempParamsApi} from '@/api/template';
import {mainStore} from '@/pinia/main';
import {getModelApi} from '@/api/model';
import {getCompApi} from '@/api/component';
import {getFileRawApi, getParamsRelationApi} from '@/api/main';
import {getCollectionListApi} from '@/api/platformCollection';
import {findParamById, shwoHideParamsTableRow, getLastBlockParamsShow, showBlockParams, postProcessingParams, filterChildrenParam} from '@/utils/dataProcess';
import uuid from '@/utils/getUUID';
import {ParamType, GroupsParamType, GroupsType, InstanceDataType, ModulesType, ConfigureJsonType, EditParamsTypeType, RelationDataType, CollectionType, ExtendBasesType} from '@/types/index';
const store = mainStore();
const ParamsOptions = defineAsyncComponent(() => import('@/components/Params/ParamsOptions.vue'));
const DataInstanceList = defineAsyncComponent(() => import('@/components/Params/DataInstanceList.vue'));
const CategoryInstanceList = defineAsyncComponent(() => import('@/components/Params/CategoryInstanceList.vue'));
const TemplateList = defineAsyncComponent(() => import('@/components/Params/TemplateList.vue'));
const ParamsGroup = defineAsyncComponent(() => import('@/components/Params/ParamsGroup.vue'));
const PlatformCommpParams = defineAsyncComponent(() => import('@/components/Params/PlatformCommpParams.vue'));
const ComponentParams = defineAsyncComponent(() => import('@/components/Params/ComponentParams.vue'));
const TabManage = defineAsyncComponent(() => import('@/components/Params/TabManage.vue'));
const LoadModel = defineAsyncComponent(() => import('@/components/LoadModel.vue'));
const props = defineProps({
  blockWidth: {
    type: Object,
    default: () => {
      return {
        lWidth: 80,
        cWidth: 315,
        rWidth: 74,
      };
    },
  },
  templateTypeId: {
    type: Number,
    required: true,
  },
  instanceDataId: {
    type: String,
    required: true,
  },
  showTabKey: {
    type: Array,
    default: () => {
      return [1];
    },
  },
  disabledTabKey: {
    type: Array,
    default: () => {
      return [];
    },
  },
  disable: {
    type: Boolean,
    default: () => {
      return false;
    },
  },
  showForm: {
    type: Boolean,
    default: () => {
      return false;
    },
  },
});

const emits = defineEmits(['loadDone']);

const {PLATFORM_TEMPLATE_ID} = configMap;

const instanceType = ref(1); // 实例列表; 对应：1->数据实例 2->图标实例 3->组件实例
const currentTab = ref(1); // 当前tab；对应：1->参数管理 2->实例列表 3->模板列表
const componentPath = ref('');
const linkInstanceId = ref(''); // link的实例ID
const useLinkInstanceRow = ref(); // 当前使用的link的参数行
const instanceListTitle = ref(''); // 实体查询ids
const currentParamsIds = ref(new Set()); // 当前要删除的参数的templateIds
const lastBlockParamsShowIds = ref(new Map()); // 删除之前块级参数下的templateIds
const deleteParamType = ref(''); // 删除参数的指令 option -> 已选参数中删除 table -> 表格中删除
const titleRef = ref(); // 参数编辑的头部dom
const formHeight = ref(0); // 参数编辑的头部高度
const extendBases = ref<ExtendBasesType[]>([]); // 继承的数据
const newAddTableRowId = ref('');
const relationData = ref<RelationDataType[]>([]);
const showName = ref(false);
const paramList = ref<ParamType[]>([]);
const scenarioGroupParams = ref<GroupsParamType[]>([]); // 场景可编辑已选参数
const scenarioTempGroupParams = ref<GroupsParamType[]>([]); // 场景可编辑参数
const scenarioParams = ref<ParamType[]>([]);
const instanceCollapse = ref('');
const expandKeys = ref(new Set()); // 参数列表展开keys
const selectedComponentId = ref('');

// 共享数据
provide('expandKeys', expandKeys);
provide('instanceType', instanceType);
provide('currentTab', currentTab);
provide('instanceListTitle', instanceListTitle);
provide('componentPath', componentPath);
provide('linkInstanceId', linkInstanceId);
provide('useLinkInstanceRow', useLinkInstanceRow);
provide('currentParamsIds', currentParamsIds);
provide('lastBlockParamsShowIds', lastBlockParamsShowIds);
provide('deleteParamType', deleteParamType);
provide('extendBases', extendBases);
provide('newAddTableRowId', newAddTableRowId);
provide('relationData', relationData);
provide('paramList', paramList);
provide('tableScenarioParams', scenarioGroupParams);
provide('instanceCollapse', instanceCollapse);
provide('showName', showName);
provide('selectedComponentId', selectedComponentId);
const compCategorys = inject<Ref<CompCategorys[]>>('compCategorys')!;
const relationTreeTab = inject<Ref<number> | undefined>('relationTreeTab', undefined);
const instanceData = inject<Ref<InstanceDataType>>('instanceData')!;
const editParamsType = inject<Ref<EditParamsTypeType> | undefined>('editParamsType', undefined); // 平台类型的参数列表 1->平台参数 2->某个组件的参数
const componentModule = inject<Ref<ModulesType> | undefined>('componentModule', undefined)!;
const currentTool = inject<Ref<Number>>('currentTool')!;
const deleteCompSignal = inject<Ref<{id: string}> | undefined>('deleteCompSignal', undefined);
const nodeNewName = inject<Ref<string> | undefined>('nodeNewName', undefined);
const extendsType = inject<Ref<string> | undefined>('extendsType', undefined);
const scenarioParamsEdit = inject<Ref<boolean> | undefined>('scenarioParamsEdit', undefined);
const scenarioGroupParamsMap = inject<Ref<Map<string, {belongType: string; belongId: string; group: GroupsParamType[]}>> | undefined>('scenarioGroupParamsMap', undefined);
const deleteKeydown = inject<Ref<boolean> | undefined>('deleteKeydown', undefined);
const isPreview = inject<Ref<boolean> | undefined>('isPreview', undefined);
let groupList: GroupsType[] = [];
const tempParamList = ref<ParamType[]>([]);
const groupParamList = ref<GroupsParamType[]>([]);
const tempGroupParamList = ref<GroupsParamType[]>([]);
const collectionList = ref<CollectionType[]>([]);
const paramsOptionsReserveRef = ref(); // 已选参数的ref
const paramsOptionsUnReserveRef = ref(); // 未选参数的ref
const paramsGroupRef = ref(); // 参数列表的ref
const dataInstanceRef = ref(); // 数据实例的ref
const templateRef = ref(); // 选择模板的ref
const categoryInstanceRef = ref(); // 复制/继承的ref
const tabKey = ref<number[]>(store.newInstanceDialog.length && !isPreview?.value ? [1, 3, 4] : [1]);
const groupCollapse = ref<string[]>([`-1`]);
provide('groupCollapse', groupCollapse);

const componentNames = ref([]); //组件名称数组
watch(
  () => props.showTabKey,
  newV => {
    tabKey.value = newV as number[];
  }
);
watch(
  () => store.staging.flag,
  newV => {
    if (newV) {
      tabKey.value = [1];
      store.staging.instanceType = instanceType.value;
    } else {
      instanceType.value = 1;
      instanceType.value = store.staging.instanceType;
    }
  }
);
watch(
  () => store.addCompCategoryId,
  newV => {
    instanceType.value = newV ? 3 : 1;
    currentTab.value = newV || (!store.staging.flag && store.staging.useLinkKey) ? 2 : 1;
    instanceListTitle.value = store.treeCurrentComp.displayName;
    selectedComponentId.value = ''; //清除选择的组件id

    console.log(currentTab.value, 'currentTab.value');
    console.log(instanceListTitle.value, ' instanceListTitle.value');
    console.log(compCategorys.value, '组件列表');
  }
);
watch(
  () => editParamsType?.value,
  newV => {
    newAddTableRowId.value = '';
    if (!newV?.type) return;
    nextTick(() => {
      formHeight.value = titleRef.value?.clientHeight || 0;
    });
    if (newV?.type === 2 && componentModule?.value.isScript) return;
    if (relationTreeTab?.value !== 1) return;
    if (newV?.type === 1) {
      getGroupParams();
    } else {
      getModuleParams();
    }
    currentTab.value = 1;
  },
  {deep: true}
);

watch(
  () => componentModule?.value?.id,
  async newV => {
    if (!newV || !componentModule.value.isScript) return;
    resetData();
    if (componentModule.value.scriptContent) return;
    if (componentModule.value.componentId) {
      const baseScriptId = (await getCompApi(componentModule.value.componentId)).data.scriptFileId;
      const baseScriptRes = await getFileRawApi(baseScriptId);
      componentModule.value.baseScriptContent = Base64.decode(baseScriptRes.data.data);
    }
    componentModule.value.scriptContent = '';
    componentModule.value.oldScriptContent = '';
    componentModule.value.fileName = '';
    const fileId = componentModule.value.scriptFileId;
    if (fileId) {
      const res = await getFileRawApi(fileId);
      if (!res.data.data) return ElMessage.error('获取文件内容失败');
      componentModule.value.scriptContent = Base64.decode(res.data.data);
      componentModule.value.oldScriptContent = Base64.decode(res.data.data);
      componentModule.value.fileName = res.data.name;
    }
  }
);
onMounted(async () => {
  console.log(instanceData.value, 'instanceData');
  // todo 处理displayName为name(汉字转拼音)
  const pinyinRes = pinyin(instanceData.value.displayName, {toneType: 'none', v: true}).split(' ').join('');

  instanceData.value.name = pinyinRes;
  emits('loadDone');
  componentNames.value = compCategorys.value.map(i => i.displayName);

  resetData();
  formHeight.value = titleRef.value?.clientHeight || 0;
  getGroupParams();
  if (editParamsType) getCollectionList();
});
// 获取模型集列表
const getCollectionList = async () => {
  collectionList.value = (await getCollectionListApi()).data.list || [];
};
// 获取平台类型下组件的参数列表并组合数据
const getModuleParams = async () => {
  resetData();

  if (isEmpty(componentModule.value)) return ElMessage.error('查询组件失败');
  console.log(componentModule.value, 'componentModule.value');
  groupList = (await getGroupsApi({templateTypeId: componentModule.value.templateTypeId})).data.list;
  paramList.value = componentModule.value.params;
  const tempParamListRes = (await getTempParamsApi({typeId: componentModule.value.templateTypeId})).data.list || [];
  postProcessingParams(tempParamListRes);
  tempParamList.value = tempParamListRes;
  // 获取模板下的互斥/关联参数id
  relationData.value = (await getParamsRelationApi({templateTypeId: props.templateTypeId})).data;

  // 先添加默认分组
  groupParamList.value.push({id: -1, name: '默认分组', children: paramList.value.filter(item => !item.groupId || !groupList.find(i => i.id === item.groupId))});
  tempGroupParamList.value.push({id: -1, name: '默认分组', children: tempParamList.value.filter(item => !item.groupId || !groupList.find(i => i.id === item.groupId))});
  // 组合未选参数(模板参数)
  tempParamList.value.forEach(tempItem => {
    if (paramList.value.find(item => item.show && item.templateParamId === tempItem.id)) tempItem.show = false;
  });

  // 根据参数的分组id组装数据
  groupList.forEach(groupItem => {
    // 组合已选参数
    const param = {...groupItem, children: paramList.value.filter(item => item.groupId === groupItem.id)};
    groupParamList.value.push(param);
    const tempParam = {...groupItem, children: tempParamList.value.filter(item => item.groupId === groupItem.id)};
    tempGroupParamList.value.push(tempParam);
  });
  console.log(groupParamList.value, 'groupParamList.value');
};
// // 获取组列表和参数列表并组合数据
// const getGroupParams = async () => {
//   resetData();
//   groupList = (await getGroupsApi({templateTypeId: props.templateTypeId})).data.list;
//   paramList.value = props.instanceDataId || instanceData.value?.copy ? (instanceData.value.params as ParamType[]) : [];
//   if (instanceData.value?.copy) delete instanceData.value.copy;
//   if (currentTool.value === 1) paramList.value = instanceData.value.params as ParamType[];
//   const tempParamListRes = (await getTempParamsApi({typeId: props.templateTypeId})).data.list || [];
//   postProcessingParams(tempParamListRes, Number(props.templateTypeId) === PLATFORM_TEMPLATE_ID);
//   tempParamList.value = tempParamListRes;
//   // 获取模板下的互斥/关联参数id
//   relationData.value = (await getParamsRelationApi({templateTypeId: props.templateTypeId})).data;
//   // 先添加默认分组
//   groupParamList.value.push({id: -1, name: '默认分组', children: paramList.value.filter(item => !item.groupId || !groupList.find(i => i.id === item.groupId))});
//   tempGroupParamList.value.push({id: -1, name: '默认分组', children: tempParamList.value.filter(item => !item.groupId || !groupList.find(i => i.id === item.groupId))});
//   // 组合未选参数(模板参数)
//   tempParamList.value.forEach(tempItem => {
//     // 如果是已有的参数 或者 实例是继承, 隐藏除了图标以外的平台绑定的参数
//     if (paramList.value.find(item => item.show && item.templateParamId === tempItem.id) || (instanceData.value.extendsId && tempItem.isParamBind && tempItem.name !== 'icon')) tempItem.show = false;
//   });
//   // 根据参数的分组id组装数据
//   groupList.forEach(groupItem => {
//     // 组合已选参数
//     const param = {...groupItem, children: paramList.value.filter(item => item.groupId === groupItem.id)};
//     groupParamList.value.push(param);
//     const tempParam = {...groupItem, children: tempParamList.value.filter(item => item.groupId === groupItem.id)};
//     tempGroupParamList.value.push(tempParam);
//   });
//   if (store.addBindParam) {
//     store.addBindParam = false;
//     for (const node of tempParamListRes) {
//       if (!node.isParamBind) continue;
//       handleAddParam(node, true);
//     }
//     newAddTableRowId.value = '';
//   }
//   const defalutParams = new Set(['heading', 'route', 'creation_time', 'empty_mass', 'payload_mass', 'length', 'width', 'height']);
//   const resulut = tempParamList.value.filter(i => defalutParams.has(i.name) && i.show);
//   resulut.forEach(i => {
//     handleAddParam(i);
//   });
//   iconName.value = paramList.value.find(i => i.name === 'icon')?.linkName as string;
//   console.log(iconName.value, 'linkName');
// };

const platformAndCompParams = ref<GroupsParamType[]>([]);
//todo 获取组列表和参数列表并组合数据（组件和参数都获取）
const getGroupParams = async () => {
  resetData();
  groupList = (await getGroupsApi({templateTypeId: props.templateTypeId})).data.list;
  paramList.value = props.instanceDataId || instanceData.value?.copy ? (instanceData.value.params as ParamType[]) : [];
  if (instanceData.value?.copy) delete instanceData.value.copy;
  if (currentTool.value === 1) paramList.value = instanceData.value.params as ParamType[];
  const tempParamListRes = (await getTempParamsApi({typeId: props.templateTypeId})).data.list || [];
  postProcessingParams(tempParamListRes, Number(props.templateTypeId) === PLATFORM_TEMPLATE_ID);
  tempParamList.value = tempParamListRes;
  // 获取模板下的互斥/关联参数id
  relationData.value = (await getParamsRelationApi({templateTypeId: props.templateTypeId})).data;
  // 先添加默认分组
  groupParamList.value.push({id: -1, name: '默认分组', children: paramList.value.filter(item => !item.groupId || !groupList.find(i => i.id === item.groupId))});
  tempGroupParamList.value.push({id: -1, name: '默认分组', children: tempParamList.value.filter(item => !item.groupId || !groupList.find(i => i.id === item.groupId))});
  // 组合未选参数(模板参数)
  tempParamList.value.forEach(tempItem => {
    // 如果是已有的参数 或者 实例是继承, 隐藏除了图标以外的平台绑定的参数
    if (paramList.value.find(item => item.show && item.templateParamId === tempItem.id) || (instanceData.value.extendsId && tempItem.isParamBind && tempItem.name !== 'icon')) tempItem.show = false;
  });
  // 根据参数的分组id组装数据
  groupList.forEach(groupItem => {
    // 组合已选参数
    const param = {...groupItem, children: paramList.value.filter(item => item.groupId === groupItem.id)};
    groupParamList.value.push(param);
    const tempParam = {...groupItem, children: tempParamList.value.filter(item => item.groupId === groupItem.id)};
    tempGroupParamList.value.push(tempParam);
  });
  if (store.addBindParam) {
    store.addBindParam = false;
    for (const node of tempParamListRes) {
      if (!node.isParamBind) continue;
      handleAddParam(node, true);
    }
    newAddTableRowId.value = '';
  }
  const defalutParams = new Set(['heading', 'route', 'creation_time', 'empty_mass', 'payload_mass', 'length', 'width', 'height']);
  const resulut = tempParamList.value.filter(i => defalutParams.has(i.name) && i.show);
  resulut.forEach(i => {
    handleAddParam(i);
  });
  iconName.value = paramList.value.find(i => i.name === 'icon')?.linkName as string;
  //todo 将平台和组件的参数进行区分
  const newPlatformGroup = {
    id: -2,
    name: '平台参数',
    children: groupParamList.value, // 原始结构保持不变
  };
  const newComponentGroup = {
    id: -3,
    name: '组件参数',
    children: [] as GroupsParamType[], // 注意类型为 GroupsParamType[]
  };
  const modules = instanceData.value.modules ? instanceData.value.modules : [];
  for (const item of modules) {
    const moduleParamsRes = await getModuleParamsReturn(item);
    console.log(moduleParamsRes);
    // 直接 push 每个分组对象，保留分组结构
    for (const iterator of moduleParamsRes) {
      newComponentGroup.children.push(iterator);
    }
  }

  platformAndCompParams.value.push(newPlatformGroup);
  platformAndCompParams.value.push(newComponentGroup);
  console.log(platformAndCompParams.value, '结果数据');
};

// 获取平台类型下组件的参数列表并组合数据
const getModuleParamsReturn = async component => {
  let paramList = [];
  let tempParamList = [];
  let groupParamList = [];
  if (isEmpty(component)) return ElMessage.error('查询组件失败');
  groupList = (await getGroupsApi({templateTypeId: component.templateTypeId})).data.list;
  paramList = component.params;
  const tempParamListRes = (await getTempParamsApi({typeId: component.templateTypeId})).data.list || [];
  postProcessingParams(tempParamListRes);
  tempParamList = tempParamListRes;
  // 获取模板下的互斥/关联参数id
  relationData.value = (await getParamsRelationApi({templateTypeId: props.templateTypeId})).data;

  // 先添加默认分组
  groupParamList.push({id: -11, name: `${component.displayName}默认分组`, children: paramList.filter(item => !item.groupId || !groupList.find(i => i.id === item.groupId))});
  tempGroupParamList.value.push({id: -11, name: `${component.displayName}默认分组`, children: tempParamList.filter(item => !item.groupId || !groupList.find(i => i.id === item.groupId))});
  // 组合未选参数(模板参数)
  tempParamList.forEach(tempItem => {
    if (paramList.find(item => item.show && item.templateParamId === tempItem.id)) tempItem.show = false;
  });

  // 根据参数的分组id组装数据
  groupList.forEach(groupItem => {
    // 组合已选参数
    const param = {...groupItem, children: paramList.filter(item => item.groupId === groupItem.id)};
    groupParamList.push(param);
    const tempParam = {...groupItem, children: tempParamList.filter(item => item.groupId === groupItem.id)};
    tempGroupParamList.value.push(tempParam);
  });
  return groupParamList;
};
// 更新参数 -> 参数改名/添加/删除
const updateParams = async () => {
  const templateTypeId = store.newInstanceDialog.length ? store.newInstanceDialog[store.newInstanceDialog.length - 1].id : editParamsType?.value.type === 2 && componentModule?.value.templateTypeId ? componentModule?.value.templateTypeId : props.templateTypeId;
  tempGroupParamList.value = [];
  groupParamList.value = [];
  groupList = (await getGroupsApi({templateTypeId})).data.list;
  const tempParamListRes = (await getTempParamsApi({typeId: templateTypeId, withChildren: true})).data.list || [];
  postProcessingParams(tempParamListRes, Number(templateTypeId) === PLATFORM_TEMPLATE_ID);
  tempParamList.value = tempParamListRes;
  // 获取模板下的互斥/关联参数id
  relationData.value = (await getParamsRelationApi({templateTypeId: props.templateTypeId})).data;
  paramList.value = paramList.value.sort((a: ParamType, b: ParamType) => a.sort - b.sort);
  updateSelectedParams(paramList.value);
  groupParamList.value.push({id: -1, name: '默认分组', children: paramList.value.filter(item => !item.groupId || !groupList.find(i => i.id === item.groupId))});
  tempGroupParamList.value.push({id: -1, name: '默认分组', children: tempParamList.value.filter(item => !item.groupId || !groupList.find(i => i.id === item.groupId))});
  // 组合未选参数(模板参数)
  tempParamList.value.forEach(tempItem => {
    if (paramList.value.find(item => item.show && item.templateParamId === tempItem.id)) tempItem.show = false;
  });
  // 根据参数的分组id组装数据
  groupList.forEach(groupItem => {
    const param = {...groupItem, children: paramList.value.filter(item => item.groupId === groupItem.id)};
    groupParamList.value.push(param);
    const tempParam = {...groupItem, children: tempParamList.value.filter(item => item.groupId === groupItem.id)};
    tempGroupParamList.value.push(tempParam);
  });

  ElMessage.success('刷新成功');
};
// 模板参数的数据更新后, 递归更新已选参数
const updateSelectedParams = (arr: ParamType[]) => {
  if (!arr.length) return;
  for (const node of arr) {
    const selected = findParamById(tempParamList.value, node.templateParamId!);
    if (selected) {
      node.groupId = selected.groupId;
      node.name = selected.name;
      node.displayName = selected.displayName;
    } else node.show = false;
    if (node?.params?.length) updateSelectedParams(node.params);
  }
};
// 参数options组件的添加回调
const handleAddParam = (item: ParamType, flag = false) => {
  // 互斥参数组
  const mutexRelation = relationData.value.filter(i => i.relation === 'mutex' && i.paramIds.includes(item.id));
  // 互斥参数id集合
  const mutexIds: string[] = mutexRelation.map(i => i.paramIds.filter(i => i !== item.id)).flat();
  const mutexParams = mutexIds.map(i => findParamById(paramList.value, i, 'templateParamId')).filter(i => i && i.show) as ParamType[];
  if (mutexParams.length) return ElMessage({message: `当前参数与已选参数 [${mutexParams.map(i => i.name).join(', ')}] 冲突`, type: 'warning', duration: 2000});
  item.show = false;
  newAddTableRowId.value = '';
  const group = groupParamList.value.find(i => i.id === (groupList.find(i => i.id === item.groupId) ? item.groupId : -1));
  if (!group) return;

  // 展开分组
  paramsOptionsReserveRef.value?.setActiveCollapse(group.id);
  const params = group.children.filter(i => i.templateParamId === item.id);
  // 之前没有添加过
  if (!params.length) {
    const copyData: ParamType = cloneDeep(item);
    copyData.templateParamId = copyData.id;
    copyData.id = uuid();
    copyData.value = copyData.defaultValue;
    if (copyData.defaultType === 'boolean') copyData.value = true;
    if (copyData.defaultType === 'struct' && copyData.configureJson?.length) {
      for (const node of copyData.configureJson as ConfigureJsonType[]) {
        node.value = node.defaultValue;
      }
    }
    // 如果是继承
    if ((editParamsType && editParamsType.value.type === 2 && componentModule && (componentModule.value.componentId || componentModule.value.editModuleId)) || instanceData.value.extendsId) {
      const baseParams: ParamType[] = (extendBases.value.map(ei => ei.data.map(di => di.children).flat()).flat() || []).reverse();
      const baseParam = baseParams.find(i => i.templateParamId === copyData.templateParamId);
      if (baseParam) {
        if (copyData.defaultType === 'struct' && copyData.configureJson?.length) {
          for (let i = 0; i < (baseParam.configureJson as ConfigureJsonType[]).length; i++) {
            (copyData.configureJson as ConfigureJsonType[])[i].value = (baseParam.configureJson as ConfigureJsonType[])[i].value;
            (copyData.configureJson as ConfigureJsonType[])[i].unitType = (baseParam.configureJson as ConfigureJsonType[])[i].unitType;
          }
        } else if (copyData.defaultType === 'link') {
          copyData.linkName = baseParam.linkName;
        }
        copyData.value = baseParam.value || '';
        copyData.unitType = baseParam.unitType;
      }
    }
    shwoHideParamsTableRow(copyData, false);
    copyData.show = true;
    paramList.value.push(copyData);
    newAddTableRowId.value = copyData.id;
    // flag -> 平台类型绑定参数, 要默认添加到参数里 如果当前是平台类型, 添加到平台类型的参数里
    if (flag) instanceData.value.params = paramList.value;
    const minSorts = group.children.filter(i => i.sort < copyData.sort);
    group.children.splice(minSorts.length, 0, copyData);
  } else {
    for (const node of params.filter(i => Array.from(currentParamsIds.value).includes(i.id))) {
      node.show = true;
      if (!newAddTableRowId.value) newAddTableRowId.value = node.id;
      if (node.isBlockParam !== 1 && node?.params?.length) {
        showBlockParams(node.params, node.id, lastBlockParamsShowIds.value);
        lastBlockParamsShowIds.value.delete(node.id);
      }
    }
  }
  instanceDataChange(); // 返回提示
  instanceCollapse.value = !editParamsType || editParamsType.value.type === 1 || store.newInstanceDialog.length ? instanceData.value.id : componentModule.value?.id || '';
  groupCollapse.value.push((item.groupId || -1).toString());
  groupCollapse.value = [...new Set(groupCollapse.value)];
};
// 参数options组件的删除回调
const handleDeleteParam = (item: ParamType) => {
  if (item.id === newAddTableRowId.value) newAddTableRowId.value = '';
  const groupId = !item.paramId ? (groupList.find(i => i.id === item.groupId) ? item.groupId : -1) : findAncestors(paramList.value, item.id)[0].groupId || -1;
  const group = groupParamList.value.find(i => i.id === groupId);
  if (!group) return;
  // 如果删除的是顶级参数, 拿组下的children
  let copyList = group.children.filter(i => i.templateParamId === item.templateParamId);
  // 如果删除的是子参数, 拿该参数兄弟参数
  if (item.paramId && item.paramId !== '0') {
    let allParamsList: ParamType[] = [];
    for (const node of groupParamList.value) {
      allParamsList = [...allParamsList, ...cloneDeep(node.children)];
    }
    copyList = findParamById(allParamsList, item.paramId)?.params?.filter(i => i.templateParamId === item.templateParamId) || [];
  }
  // 如果隐藏的是块级参数, 记录块级参数下所有显示的子参数, 下次将该块级参数显示后, 使用的是最后一次的状态
  for (const node of copyList) {
    if (node.show) currentParamsIds.value.add(node.id);
    if (node.isBlockParam !== 1 && node?.params?.length) getLastBlockParamsShow(node.params, node.id, lastBlockParamsShowIds.value);
  }
  // 如果删除的是场景可编辑参数, 同步删除
  if ((deleteParamType.value === 'option' || copyList.filter(i => i.show).length === 1) && item.scenarioEditable && scenarioGroupParamsMap) {
    const id = editParamsType?.value.type === 1 ? instanceData.value.id : componentModule.value.id;
    const nodeValue = scenarioGroupParamsMap.value.get(id);
    if (nodeValue) {
      nodeValue.group.forEach(i => {
        i.children = i.children.filter(i => i.templateParamId !== item.templateParamId);
      });
    }
  }
  // 如果是options组件删除的, 删除所有的相同的参数
  if (deleteParamType.value === 'option') {
    for (const node of copyList) shwoHideParamsTableRow(node, false);
  } else {
    if (copyList.filter(i => i.show).length > 1) currentParamsIds.value.delete(item.id);
    // 表格的删除, 只删除本身
    shwoHideParamsTableRow(item, false);
  }

  instanceDataChange(); // 返回提示
  // 当所有同样的参数都被删除, 显示添加参数组件上的参数
  const every = group.children.filter(i => i.templateParamId === item.templateParamId).every(i => !i.show);
  const param = findParamById(tempParamList.value, item.templateParamId!);
  if (!param) return;
  param.show = every;
};
// 复制参数
const handleCopyParam = (data: {row: ParamType; list: ParamType[]}) => {
  newAddTableRowId.value = '';
  const copyData = cloneDeep(data.row);
  copyData.id = uuid();
  if (copyData?.params?.length) updateId(copyData.params, copyData.id);
  newAddTableRowId.value = copyData.id;
  const sameParams = data.list.filter(i => i.templateParamId === copyData.templateParamId);
  const lastParam = sameParams[sameParams.length - 1];
  const index = data.list.findIndex(i => i.id === lastParam.id);
  data.list.splice(index + 1, 0, copyData);
  if (!copyData.paramId || copyData.paramId === '0') paramList.value.push(copyData);
  instanceDataChange(); // 返回提示
};
// 复制参数时， 递归修改参数id
const updateId = (arr: ParamType[], pId: string) => {
  if (!arr?.length) return;
  for (const node of arr) {
    node.id = uuid();
    currentParamsIds.value.add(node.id);
    node.paramId = pId;
    if (node?.params?.length) updateId(node.params, node.id);
  }
};
// 平台/组件属性名称修改事件
const handleInputChange = (val: string) => {
  if (!nodeNewName) return;
  nodeNewName.value = val;
  instanceDataChange(); // 返回提示
};
// 重置数据
const resetData = () => {
  // lastBlockParamsShowIds.value = new Map();
  // currentParamsIds.value = new Set();
  groupParamList.value = [];
  tempGroupParamList.value = [];
  paramList.value = [];
  extendBases.value = [];
  platformAndCompParams.value = [];
};
// 设置表格展开的key
const setdefaultExpandKeys = (arr: ParamType[]) => {
  for (const node of arr) {
    if (node.params?.length) {
      expandKeys.value.add(node.id);
      setdefaultExpandKeys(node.params);
    }
  }
};
// 确认选择模板/复制
const confimCopy = async (data: {id: string; tool: number}) => {
  groupCollapse.value = ['-1'];
  const {id, tool} = data;
  instanceData.value.extendsId = '';
  let res;
  switch (tool) {
    case 2:
      res = (await getModelApi(id)).data;
      break;
    case 3:
      res = (await getCompApi(id)).data;

      break;
    default:
      break;
  }
  if (!instanceData.value.name) instanceData.value.name = `${res.name}_copy`;
  if (!instanceData.value.displayName) instanceData.value.displayName = `${res.displayName}_copy`;
  instanceData.value.templateTypeId = res.templateTypeId;
  if (res.isScript) {
    instanceData.value.scriptFileId = res.scriptFileId;
    instanceData.value.isScript = res.isScript;
  } else {
    instanceData.value.extendsId = res.extendsId;
    instanceData.value.templateTypeId = res.templateTypeId;

    if (instanceData.value.extendsId) paramsGroupRef.value.getBaseParams({tool, tId: instanceData.value.templateTypeId, cId: instanceData.value.extendsId});
    const copyParams = res.params || [];
    // 预处理数据
    postProcessingParams(copyParams);
    setdefaultExpandKeys(copyParams);
    // 清空数据
    lastBlockParamsShowIds.value = new Map();
    currentParamsIds.value = new Set();
    groupParamList.value = [];
    paramList.value = [];

    paramList.value = copyParams;
    groupParamList.value.push({id: -1, name: '默认分组', children: paramList.value.filter(item => !item.groupId)});
    // 组合未选参数(模板参数)
    tempParamList.value.forEach(tempItem => {
      tempItem.show = true;
      if (paramList.value.find(item => item.templateParamId === tempItem.id)) tempItem.show = false;
    });
    // 根据参数的分组id组装数据
    groupList.forEach(groupItem => {
      // 组合已选参数
      const param = {...groupItem, children: paramList.value.filter(item => item.groupId === groupItem.id)};
      groupParamList.value.push(param);
    });
  }
  currentTab.value = 1;
  store.openHintValue(store.newInstanceDialog[store.newInstanceDialog.length - 1]?.id || '');
};
const extendConfim = (data: {templateTypeId: number; extendsId: string; name: string; displayName: string}) => {
  // 清空数据
  extendBases.value = [];
  lastBlockParamsShowIds.value = new Map();
  currentParamsIds.value = new Set();
  groupParamList.value = [];
  paramList.value = [];
  instanceData.value.templateTypeId = data.templateTypeId;
  instanceData.value.extendsId = data.extendsId;
  instanceData.value.params = [];
  if (!instanceData.value.name || instanceData.value.name.includes('_copy')) instanceData.value.name = `${data.name}_new`;
  if (!instanceData.value.displayName || instanceData.value.displayName.includes('_copy')) instanceData.value.displayName = `${data.displayName}_new`;
  groupParamList.value.push({id: -1, name: '默认分组', children: []});
  tempParamList.value.forEach(i => {
    shwoHideParamsTableRow(i, true);
  });
  if (extendsType) extendsType.value = data.name;
  paramsGroupRef.value.getBaseParams({tool: currentTool.value, tId: data.templateTypeId, cId: data.extendsId});
  store.openHintValue(store.newInstanceDialog[store.newInstanceDialog.length - 1]?.id || '');
  currentTab.value = 1;
};
// 重置tab
const resetTab = () => {
  currentTab.value = 1;
  tabKey.value = [1];
};
// 删除平台组件
const deleteComp = () => {
  ElMessageBox.confirm('确定要删除吗?', '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      deleteCompSignal!.value.id = componentModule.value.id;
      if (scenarioGroupParamsMap?.value.get(componentModule.value.id)) scenarioGroupParamsMap?.value.delete(componentModule.value.id);
      resetTab();
      instanceListTitle.value = '';
      componentPath.value = '';
      instanceDataChange(); // 返回提示
    })
    .finally(() => {
      deleteKeydown!.value = false;
    });
};
// 提示change
const instanceDataChange = () => {
  store.openHintValue(store.newInstanceDialog[store.newInstanceDialog.length - 1]?.id || '');
};
const tabChange = (val: number) => {
  // if (val !== 2) useLinkInstanceRow.value = null;
};
// 获取集合名称
const getCollectionName = (item: string) => {
  return collectionList.value.find(i => i.id === item)?.name || '';
};
// 删除所属模型集
const closeCollection = (item: string) => {
  instanceData.value.collectionIds = instanceData.value.collectionIds?.filter(i => i !== item);
  instanceDataChange();
};
// 标签内容改变事件
const tagChange = () => {
  instanceDataChange();
  // 处理标签最后一位
  if (!instanceData.value.tag) return;
  instanceData.value.tag = instanceData.value.tag
    .split(';')
    .filter(i => i)
    .join(';');
};

// 组装场景可编辑参数
const setScenarioParams = () => {
  if (!scenarioParamsEdit || scenarioParamsEdit.value || !editParamsType || !scenarioGroupParamsMap) return;
  resetTab();
  const {type} = editParamsType.value;
  const id = type === 1 ? instanceData.value.id : componentModule.value.id;
  const groupParamsMap = scenarioGroupParamsMap.value.get(id);
  if (!groupParamsMap) {
    const group = groupList.map(i => ({id: i.id, name: i.name, children: []}));
    group.unshift({id: -1, name: '默认分组', children: []});
    scenarioGroupParamsMap.value.set(id, {belongType: type.toString(), belongId: id, group});
  }
  scenarioGroupParams.value = scenarioGroupParamsMap.value.get(id)!.group;
  /**
   * 1. 获取已选参数, 生成一维数组
   * 2. 获取继承参数, 生成一维数组
   * 3. 合并两个一维数组, 并过滤掉temId重复的
   */
  // 已选参数一维数组
  const selectedParams: ParamType[] = groupParamList.value.map(i => i.children.filter(ci => ci.scenarioEditable && ci.show && !ci.isParamBind)).flat();

  // 继承参数一维数组
  let extendParams: ParamType[] = [];
  if ((editParamsType.value.type === 1 && instanceData.value.extendsId) || (editParamsType.value.type === 2 && componentModule && ((componentModule.value.isRef && componentModule.value.componentId) || componentModule.value.editModuleId))) {
    extendParams = (extendBases.value.map(ei => ei.data.map(di => di.children.filter(ci => ci.scenarioEditable)).flat()).flat() || []).reverse();
  }
  // 过滤掉重复参数后的场景可编辑参数
  scenarioParams.value = Array.from(new Set([...selectedParams, ...extendParams].map(i => i.templateParamId)))
    .map(id => [...selectedParams, ...extendParams].find(i => id === i.templateParamId)!)
    .filter(
      i =>
        !scenarioGroupParams.value
          .map(i => i.children)
          .flat()
          .find(ci => ci.templateParamId === i.templateParamId)
    )
    .sort((a, b) => a.sort - b.sort);
  scenarioTempGroupParams.value = [{id: -1, name: '默认分组', children: cloneDeep(scenarioParams.value.filter(item => !item.groupId || !groupList.find(i => i.id === item.groupId)))}];
  // 根据参数的分组id组装数据
  groupList.forEach(groupItem => {
    const tempParam = {...groupItem, children: cloneDeep(scenarioParams.value.filter(item => item.groupId === groupItem.id))};
    scenarioTempGroupParams.value.push(tempParam);
  });
  // 清空子参数
  filterChildrenParam(scenarioTempGroupParams.value.map(i => i.children).flat() || []);
  scenarioParamsEdit.value = true;
};
// 场景参数添加
const handleAddScenarioParams = (item: ParamType) => {
  const group = scenarioGroupParams.value.find(i => i.id === (groupList.find(i => i.id === item.groupId) ? item.groupId : -1));
  const tempGroup = scenarioTempGroupParams.value.find(i => i.id === (groupList.find(i => i.id === item.groupId) ? item.groupId : -1));
  if (!group || !tempGroup) return;
  const minSorts = group.children.filter(i => i.sort < item.sort);
  group.children.splice(minSorts.length, 0, item);
  tempGroup.children = tempGroup.children.filter(i => i.templateParamId !== item.templateParamId);
  instanceDataChange();
};
// 场景参数删除
const handleDeleteScenarioParam = (item: ParamType) => {
  /**
   * 1. 找分组
   * 2. 如果是顶级参数, 在分组下的children删除
   * 3. 如果是子参数, 找到父参数, 在父参数的params下删除
   */
  if (item.paramId) {
    const params = scenarioGroupParams.value.map(i => i.children).flat() || [];
    const ancestors = findAncestors(params, item.id);
    if (!ancestors.length) return;
    ancestors[ancestors.length - 1].params = ancestors[ancestors.length - 1].params.filter(i => i.templateParamId !== item.templateParamId);
  } else {
    const group = scenarioGroupParams.value.find(i => i.id === (groupList.find(i => i.id === item.groupId) ? item.groupId : -1));
    const tempGroup = scenarioTempGroupParams.value.find(i => i.id === (groupList.find(i => i.id === item.groupId) ? item.groupId : -1));
    if (!group || !tempGroup) return;
    const minSorts = tempGroup.children.filter(i => i.sort < item.sort);
    tempGroup.children.splice(minSorts.length, 0, item);
    group.children = group.children.filter(i => i.templateParamId !== item.templateParamId);
  }
  instanceDataChange();
};
// 中英切换
const switchLanguage = () => {
  showName.value = !showName.value;
  switch (currentTab.value) {
    case 1:
      paramsOptionsReserveRef.value.setLanguage();
      paramsOptionsUnReserveRef.value.setLanguage();
      break;
    case 2:
      dataInstanceRef.value.setLanguage();
      break;
    case 3:
      templateRef.value.setLanguage();
      break;
    case 4:
      categoryInstanceRef.value.setLanguage();
      break;
    default:
      break;
  }
};
// 刷新
const handleUpdate = () => {
  switch (currentTab.value) {
    case 1:
      updateParams();
      break;
    case 2:
      dataInstanceRef.value.getInstanceList(true);
      break;
    case 3:
      templateRef.value.getSysTempList(true);
      break;
    case 4:
      categoryInstanceRef.value.getList(true);
      break;
    default:
      break;
  }
};
const clearData = () => {
  paramList.value = [];
  groupParamList.value = [];
  extendBases.value = [];
  scenarioGroupParams.value = [];
  scenarioTempGroupParams.value = [];
  tempGroupParamList.value = [];
  scenarioParams.value = [];
};
const findAncestors = (tree: ParamType[], idToFind: string, ancestors: ParamType[] = []): ParamType[] => {
  for (const node of tree) {
    if (node.id === idToFind) {
      return ancestors;
    }
    if (node.params) {
      const foundAncestors = findAncestors(node.params, idToFind, [...ancestors, node]);
      if (foundAncestors.length > 0) {
        return foundAncestors;
      }
    }
  }
  return [];
};
// 图标名称
const iconName = ref('');
defineExpose({
  useLinkInstanceRow,
  extendBases,
  groupParamList,
  resetTab,
  deleteComp,
  clearData,
});
</script>
<template>
  <div class="model-view">
    <div class="title">三维模型</div>
    <div class="thumb">
      <LoadModel :icon-name="iconName" />
    </div>
  </div>
  <ComponentParams v-if="componentNames.includes(instanceListTitle)" :class="[currentTab === 2 ? 'order-right' : '']" />

  <div v-else class="params-box left height_100" :style="{width: `43%`}">
    <div v-if="showForm" ref="titleRef">
      <el-row class="tab-box pad_h_15 align-items flex-x-between box-title">
        <el-row class="align-items">
          <el-tooltip v-if="editParamsType && editParamsType.type === 2" effect="dark" :hide-after="0" :enterable="false" :content="componentModule.isRef ? '引用' : '复制'" placement="top">
            <img class="mar_r_5" :src="`/images/componentType_${componentModule && componentModule.isRef ? 'quote' : 'copy'}.png`" alt="" />
          </el-tooltip>

          <span>{{ editParamsType && editParamsType.type === 1 ? '模型属性' : '组件属性' }}</span>
          <el-row v-if="editParamsType && editParamsType.type" class="align-items pointer pad_h_10 mar_l_10 scenario-text">
            <!-- <el-row class="align-items" @click="scenarioParamsEdit = false">
              <img :src="`/images/${scenarioParamsEdit ? 'd' : 's'}_radio_icon.png`" alt="" />
              <span :style="{color: !scenarioParamsEdit ? '#afd6ff' : '#fff'}">{{ editParamsType && editParamsType.type === 1 ? '平台' : '组件' }}参数配置</span>
            </el-row> -->
            <!-- <span> &nbsp;/&nbsp; </span> -->
            <!-- <el-row class="align-items" @click="setScenarioParams">
              <img :src="`/images/${scenarioParamsEdit ? 's' : 'd'}_radio_icon.png`" alt="" />
              <span :style="{color: scenarioParamsEdit ? '#afd6ff' : '#fff'}">场景可编辑参数配置</span>
            </el-row> -->
          </el-row>
        </el-row>
        <img v-if="editParamsType && editParamsType.type === 2 && componentModule && !componentModule.editModuleId" class="pointer" src="/images/table_delete_icon.png" alt="" title="删除组件" @click="deleteComp" />
      </el-row>
      <el-form :model="instanceData" class="instance-form" label-width="100px">
        <template v-if="editParamsType && editParamsType.type === 1">
          <el-row class="mar_b_5">
            <!-- <el-col :span="12">
              <el-form-item class="mar_r_20" label="名称">
                <el-col :span="24"> <el-input v-model.trim="instanceData.name" size="small" @change="instanceDataChange" /></el-col>
              </el-form-item>
            </el-col> -->
            <el-col :span="12"
              ><el-form-item class="mar_r_20" label="名称">
                <el-col :span="24"> <el-input v-model.trim="instanceData.displayName" size="small" @change="handleInputChange" /> </el-col></el-form-item
            ></el-col>
          </el-row>
          <el-row class="mar_b_5">
            <el-col :span="12">
              <el-col :span="24">
                <el-form-item class="mar_r_20" label="标签"> <el-input v-model.trim="instanceData.tag" placeholder="多个标签请使用分号(;)分割" size="small" @change="tagChange" /> </el-form-item></el-col
            ></el-col>
            <!-- <el-col :span="12">
              <el-form-item label="所属模型集" class="mar_r_20">
                <el-col :span="24">
                  <el-select v-model="instanceData.collectionIds" placeholder="请选择" multiple suffix-icon="CaretBottom" @change="instanceDataChange">
                    <el-option v-for="item in collectionList" :key="item.id" :label="item.name" :value="item.id" :disabled="item.id === '0'" />
                    <template #tag>
                      <el-tag v-for="item in instanceData.collectionIds" :key="item" :closable="item !== '0'" effect="dark" @close="closeCollection(item)">{{ getCollectionName(item) }} </el-tag>
                    </template>
                  </el-select>
                </el-col>
              </el-form-item>
            </el-col> -->
          </el-row>
        </template>
        <template v-else>
          <el-row class="mar_b_5">
            <!-- <el-col :span="12">
              <el-form-item class="mar_r_20" label="名称">
                <el-col :span="24">
                  <el-input v-model.trim="componentModule.name" :disabled="editParamsType && !editParamsType.type" size="small" @change="instanceDataChange" />
                </el-col>
              </el-form-item>
            </el-col> -->
            <el-col :span="12">
              <el-form-item class="mar_r_20" label="名称">
                <el-col :span="24"><el-input v-model.trim="componentModule.displayName" :disabled="editParamsType && !editParamsType.type" size="small" @change="handleInputChange" /></el-col>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="mar_r_20" label="类型">
                <el-col :span="24">
                  <el-input v-if="componentModule.componentId && extendBases.length" v-model="extendBases[extendBases.length - 1].name" size="small" disabled />
                  <el-input v-else v-model="componentModule.templateTypeName" size="small" disabled />
                </el-col>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- <el-row> </el-row> -->
        </template>
      </el-form>
    </div>
    <el-row v-if="editParamsType && editParamsType.type === 2 && !store.newInstanceDialog.length" class="height_100 width_100" :style="{height: instanceData && editParamsType ? `calc(100% - ${formHeight}px)` : '100%'}">
      <ParamsGroup v-if="scenarioParamsEdit" ref="paramsGroupRef" :disable="props.disable" :group-list="scenarioGroupParams" @handle-delete-scenario-param="handleDeleteScenarioParam" />
      <ParamsGroup v-else ref="paramsGroupRef" :disable="props.disable" :group-list="groupParamList" @handle-delete-param="handleDeleteParam" @handle-copy-param="handleCopyParam" />
    </el-row>
    <el-row v-else class="height_100 width_100" :style="{height: instanceData && editParamsType ? `calc(100% - ${formHeight}px)` : '100%'}">
      <!-- <ParamsGroup v-if="scenarioParamsEdit" ref="paramsGroupRef" :disable="props.disable" :group-list="scenarioGroupParams" @handle-delete-scenario-param="handleDeleteScenarioParam" />
      <ParamsGroup v-else ref="paramsGroupRef" :disable="props.disable" :group-list="groupParamList" @handle-delete-param="handleDeleteParam" @handle-copy-param="handleCopyParam" /> -->
      <PlatformCommpParams ref="paramsGroupRef" :disable="props.disable" :group-list="platformAndCompParams" @handle-delete-param="handleDeleteParam" @handle-copy-param="handleCopyParam" />
    </el-row>
  </div>

  <!-- 模型介绍、三维模型部分 -->
  <template v-if="currentTab === 1 && !store.newInstanceDialog.length && editParamsType && editParamsType.type === 1">
    <div class="center model-details">
      <div class="model-introduce">
        <div class="title">模型介绍</div>
        <p class="desc">
          <el-input v-model="instanceData.version" type="textarea"></el-input>
          <!-- {{ `${instanceData.version}` }} -->
        </p>
      </div>
    </div>
  </template>

  <template v-else>
    <!-- 可选参数部分(右侧) -->
    <div v-if="currentTab === 1" class="optional-params center dis-flex flex-dir-column align-items height_100" :class="[props.disable ? 'disable-edit' : '']" :style="{width: `${blockWidth.cWidth}px`}">
      <ParamsOptions
        ref="paramsOptionsReserveRef"
        type="input"
        :title="scenarioParamsEdit ? '已选场景可编辑参数' : '已选参数'"
        :group-param-list="scenarioParamsEdit ? scenarioGroupParams : groupParamList"
        height="calc(60% - 4px - 25px)"
        @handle-delete-param="handleDeleteParam"
        @handle-delete-scenario-param="handleDeleteScenarioParam"
      />
      <ParamsOptions
        ref="paramsOptionsUnReserveRef"
        :title="scenarioParamsEdit ? '场景可编辑参数' : '添加参数'"
        :group-param-list="scenarioParamsEdit ? scenarioTempGroupParams : tempGroupParamList"
        filterable
        height="calc(40%)"
        @handle-add-param="handleAddParam"
        @handle-add-scenario-params="handleAddScenarioParams"
      />
    </div>
    <!-- 组件分类列表(组件挂载的可选列表) -->
    <div v-else-if="currentTab === 2" class="component-list center height_100" :style="{width: `${blockWidth.cWidth}px`}">
      <DataInstanceList ref="dataInstanceRef" />
    </div>
    <!-- 实例模板列表 -->
    <div v-else-if="currentTab === 3" class="center height_100" :style="{width: `${blockWidth.cWidth}px`}">
      <TemplateList ref="templateRef" :template-type-id="props.templateTypeId" @confim="confimCopy" />
    </div>
    <!-- 实例模板列表 -->
    <div v-else-if="currentTab === 4" class="center height_100" :style="{width: `${blockWidth.cWidth}px`}">
      <CategoryInstanceList ref="categoryInstanceRef" :template-type-id="props.templateTypeId" @copy-confim="confimCopy" @extend-confim="extendConfim" />
    </div>

    <!-- <el-row class="right height_100 flex-dir-column flex-x-between align-items pad_b_10" :class="props.disable ? 'disable-edit' : ''" :style="{width: `${blockWidth.rWidth}px`}">
      <TabManage :show-tab-key="tabKey" :disabled-tab-key="disabledTabKey" @change="tabChange" />
      <el-dropdown trigger="click" :hide-on-click="false">
        <el-icon color="#afd6ff" class="pointer" size="20"><MoreFilled /></el-icon>
        <template #dropdown>
          <el-dropdown-item>
            <p class="pointer" :style="{padding: '0 10px', lineHeight: '1.4'}" @click="switchLanguage">
              <span :style="{color: showName ? '#afd6ff' : '#fff'}">英</span>
              <span> &nbsp;/&nbsp; </span>
              <span :style="{color: !showName ? '#afd6ff' : '#fff'}">中</span>
            </p>
          </el-dropdown-item>
          <el-dropdown-item v-if="store.newInstanceDialog.length ? true : !scenarioParamsEdit" @click="handleUpdate">
            <el-icon class="mar_l_5 pointer"><RefreshRight /></el-icon> 刷新
          </el-dropdown-item>
        </template>
      </el-dropdown>
    </el-row> -->
  </template>
</template>
<style lang="less" scoped>
.left {
  margin: 0 10px;
  background: url('/public/images/box_bg.png') no-repeat;
  transition: width 0.5s ease;
  background-size: 100% 100%;

  .tab-box {
    height: 31px;
    font-family: Medium;
    //     border-bottom: 1px solid #2059d3;
    box-shadow: inset 4px 0 20px -2px rgb(32 89 211 / 45%), inset -4px 0 20px -2px rgb(32 89 211 / 45%);
  }

  .scenario-text {
    /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
    font-family: Regular;

    img {
      scale: 0.7;
      margin-right: 3px;
    }

    span {
      font-size: 15px;
    }
  }

  .instance-form {
    padding: 15px 10px;
  }

  :deep(.el-form-item) {
    margin-bottom: 0;

    .el-form-item__label {
      padding: 0 6px 0 0;
    }
  }
}

.model-view {
  width: 40%;
  height: 100%;
  border: 1px solid #2059d3;

  .title {
    height: 37px;
    padding: 0 17px;
    font-size: 16px;
    line-height: 36px;
    color: #66a8ed;
    border-bottom: 1px solid #2059d3;
    //     border-bottom: 1px solid #2059d3;
    box-shadow: inset 4px 0 20px -2px rgb(32 89 211 / 45%), /* 左侧向内阴影 */ inset -4px 0 20px -2px rgb(32 89 211 / 45%); /* 右侧向内阴影 */
  }

  .thumb {
    width: 100%;
    height: calc(100% - 37px);
  }
}

.center {
  transition: width 0.5s ease;
}

.model-details {
  flex: 1;
  height: 100%;

  .model-introduce {
    height: calc(100% - 5px);
    margin-bottom: 10px;
    font-size: 14px;
    border: 1px solid #2059d3;
    border-top: 0 !important;

    .title {
      height: 37px;
      padding: 0 17px;
      font-size: 16px;
      line-height: 36px;
      color: #66a8ed;
      border-bottom: 1px solid #2059d3;
      //     border-bottom: 1px solid #2059d3;
      box-shadow: inset 4px 0 20px -2px rgb(32 89 211 / 45%), /* 左侧向内阴影 */ inset -4px 0 20px -2px rgb(32 89 211 / 45%); /* 右侧向内阴影 */
    }

    .desc {
      max-height: calc(100% - 37px);
      padding: 10px;
      overflow: auto;
      line-height: 1.3;
      white-space: pre-wrap;

      :deep(.el-textarea__inner) {
        height: calc(100vh - 115px);
      }
    }
  }
}

.right {
  transition: width 0.5s ease;
}

/* 布局顺序控制 */
.order-left {
  order: 1 !important;
}

.order-right {
  order: 2 !important;
}
</style>
