<template>
  <div class="entityConfigBox">
    <el-row class="title">
      <span>实体配置</span>
    </el-row>
    <div class="contentBox">
      <el-row class="row">
        <el-row
          :class="['col', entityConfigCommunication ? 'selected' : '']"
          @click="changeCommunication(!entityConfigCommunication)"
        >
          <span class="label"> 通讯 </span>
        </el-row>
        <el-row
          :class="['col', entityConfigProb ? 'selected' : '']"
          @click="changeProb(!entityConfigProb)"
        >
          <span class="label">探测</span>
        </el-row>
      </el-row>
      <el-row class="row">
        <el-row
          :class="['col', entityConfigFire ? 'selected' : '']"
          @click="changeFire(!entityConfigFire)"
        >
          <span class="label">交火</span>
        </el-row>
        <el-row
          :class="['col', entityConfigTrail ? 'selected' : '']"
          @click="changeTrail(!entityConfigTrail)"
        >
          <span class="label">尾迹</span>
        </el-row>
      </el-row>
      <el-row class="row">
        <el-row
          :class="['col', entityConfigLabel ? 'selected' : '']"
          @click="changeLabel(!entityConfigLabel)"
        >
          <span class="label">标牌</span>
        </el-row>
        <el-row
          :class="['col', entityConfigSensor ? 'selected' : '']"
          @click="changeSensor(!entityConfigSensor)"
        >
          <span class="label">雷达</span>
        </el-row>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, inject, computed } from 'vue'
import { useStore } from 'vuex'
const store = useStore()

const entityConfigCommunication = computed({
  get: () => store.state.simulationConfig.entityConfigCommunication,
  set: v => store.commit('SET_ENTITY_CONFIG_COMMUNICATION', v),
})
const entityConfigProb = computed({
  get: () => store.state.simulationConfig.entityConfigProb,
  set: v => store.commit('SET_ENTITY_CONFIG_PROB', v),
})
const entityConfigFire = computed({
  get: () => store.state.simulationConfig.entityConfigFire,
  set: v => store.commit('SET_ENTITY_CONFIG_FIRE', v),
})
const entityConfigTrail = computed({
  get: () => store.state.simulationConfig.entityConfigTrail,
  set: v => store.commit('SET_ENTITY_CONFIG_TRAIL', v),
})
const entityConfigLabel = computed({
  get: () => store.state.simulationConfig.entityConfigLabel,
  set: v => store.commit('SET_ENTITY_CONFIG_LABEL', v),
})
const entityConfigSensor = computed({
  get: () => store.state.simulationConfig.entityConfigSensor,
  set: v => store.commit('SET_ENTITY_CONFIG_SENSOR', v),
})

const currentEntityId = inject('currentEntityId', ref(''))

watch(
  () => currentEntityId.value,
  val => {
    if (!val) return
    let retry = 0
    const tryUpdate = () => {
      let found = false
      // 通讯
      if (getInitiativeEffects(val, 'comm').some(effect => effect.entity.show)) {
        entityConfigCommunication.value = true
        found = true
      } else {
        entityConfigCommunication.value = false
      }
      // 探测
      if (getInitiativeEffects(val, 'detect').some(effect => effect.entity.show)) {
        entityConfigProb.value = true
        found = true
      } else {
        entityConfigProb.value = false
      }
      // 交火
      if (getInitiativeEffects(val, 'fire').some(effect => effect.entity.show)) {
        entityConfigFire.value = true
        found = true
      } else {
        entityConfigFire.value = false
      }
      // 尾迹
      const trail = window.modelManager.trailPrimitive.trails.get(val)
      if (trail?.polyLine) {
        entityConfigTrail.value = trail.polyLine.show
        found = true
      } else {
        entityConfigTrail.value = false
      }
      // 标牌
      const labelEffect = window.viewer.entities.getById(val)
      if (labelEffect?.label) {
        entityConfigLabel.value = labelEffect.label.show?.getValue() || false
        found = true
      } else {
        entityConfigLabel.value = false
      }
      // 雷达
      const sensorEffect = (window as any).effectManager.effects.get(`${val}_sensor`)
      if (sensorEffect?.ellipsoid) {
        entityConfigSensor.value = sensorEffect?.ellipsoid.show
        found = true
      } else {
        entityConfigSensor.value = false
      }
      if (!found && retry < 3) {
        retry++
        setTimeout(tryUpdate, 200)
      }
    }
    tryUpdate()
  }
)
watch(
  () => store.state.simulationConfig.taskStatus,
  val => {
    if (val === 1) {
      entityConfigCommunication.value = true
      entityConfigProb.value = true
      entityConfigFire.value = true
      entityConfigTrail.value = true
      entityConfigLabel.value = true
      entityConfigSensor.value = true
    }
  }
)

// 改变通讯
const changeCommunication = (val: boolean) => {
  const effects = getInitiativeEffects(currentEntityId.value, 'comm')
  entityConfigCommunication.value = val
  if (!effects.length) return
  effects.forEach(effect => {
    effect.entity.show = val
  })
}
// 改变探测
const changeProb = (val: boolean) => {
  const effects = getInitiativeEffects(currentEntityId.value, 'detect')
  entityConfigProb.value = val
  if (!effects.length) return
  effects.forEach(effect => {
    effect.entity.show = val
  })
}
// 改变交火
const changeFire = (val: boolean) => {
  const effects = getInitiativeEffects(currentEntityId.value, 'fire')
  entityConfigFire.value = val
  if (!effects.length) return
  effects.forEach(effect => {
    effect.entity.show = val
  })
}

// 改变尾迹
const changeTrail = (val: boolean) => {
  const trail = window.modelManager.trailPrimitive.trails.get(
    currentEntityId.value
  )
  entityConfigTrail.value = val
  if (!trail) return
  trail.polyLine.show = val
}
// 改变标牌
const changeLabel = (val: boolean) => {
  const effect = window.viewer.entities.getById(currentEntityId.value)
  entityConfigLabel.value = val
  if (!effect) return
  effect.label.show.setValue(val)
}
// 改变雷达
const changeSensor = (val: boolean) => {
  const effect = (window as any).effectManager.effects.get(
    currentEntityId.value + '_' + 'sensor'
  )
  entityConfigSensor.value = val
  if (!effect) return
  effect.ellipsoid.show = val
  effect.ellipsoidOutline.show = val
}

// 获取与当前实体主动发出的线
function getInitiativeEffects(entityId: string, type: string) {
  const effects = window.effectManager.effects
  const relatedCommunicationEffects: any = []
  effects.forEach((effect, key) => {
    // 通信线id格式为 a_b_comm
    if (key.endsWith(`_${type}`) && key.split('_')[0] === entityId) {
      relatedCommunicationEffects.push(effect)
    }
  })
  return relatedCommunicationEffects
}
</script>

<style lang="less" scoped>
.entityConfigBox {
  height: 197px;
  position: absolute;
  width: 100%;
  bottom: 0;
  background: url('/image/box_bg.png') no-repeat 0px 37px;
  background-size: 100% 100%;
  .title {
    height: 37px;
    font-size: 16px;
    font-family: Bold;
    font-style: oblique;
    align-items: center;
    padding-left: 35px;
    padding-bottom: 4px;
    background: url('/image/title_bg.png') no-repeat;
    span {
      background: url('/image/title_font_bg.png') no-repeat 15px 5px;
    }
  }
  .contentBox {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    height: calc(100% - 32px);
    padding: 0 10px;
    .row {
      .col {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 120px;
        height: 49px;
        margin-left: 20px;
        background: url('/image/config_bg.png') no-repeat 0 0 / 100% 100%;
        .icon {
          margin-right: 5px;
        }
        .label {
          margin-right: 15px;
          font-size: 15px;
        }
        .selected {
          cursor: pointer;
        }
      }
      .col:nth-child(odd) {
        margin-right: 10px;
      }
      .selected {
        background: url('/image/config_selected_bg.png') no-repeat 0 0 / 100%
          100% !important;
      }
    }
  }
}
</style>
