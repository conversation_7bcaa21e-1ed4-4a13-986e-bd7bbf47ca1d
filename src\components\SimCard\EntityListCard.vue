<template>
  <div class="entityCardBox">
    <el-row class="title">
      <span>实体列表</span>
    </el-row>
    <div class="contentBox">
      <el-row class="tabs">
        <div
          v-for="item in sideList"
          :key="item.value"
          class="tab"
          :style="{ color: item.color }"
          :class="[{ active: currentSide === item.value }]"
          @click="currentSide = item.value"
        >
          {{ item.label }}
        </div>
      </el-row>
      <template v-if="sideEntities.length">
        <div class="entityList">
          <div
            v-for="item in sideEntities"
            :key="item"
            class="entity"
            :title="item"
            :style="{
              color: sideColor,
              background: item === currentEntityId ? '#0175b8' : '',
            }"
            @click="handleNodeClick(item)"
            @dblclick="handleDbNodeClick(item)"
          >
            {{ item }}
          </div>
        </div>
      </template>
      <template v-else>
        <div class="emptyBox">
          <Empty />
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, inject } from 'vue'
import Empty from '@/components/Empty.vue'
const emit = defineEmits(['gClick', 'gDbClick'])
import { useStore } from 'vuex'

const store = useStore()
const props = defineProps({
  entitiesList: {
    type: Object,
    default: '',
  },
})

const sideList = [
  {
    label: '红方',
    value: 'red',
    color: '#fe7f7f',
  },
  {
    label: '蓝方',
    value: 'blue',
    color: '#00a4c5',
  },
]
const sideEntities = computed(() => {
  return props.entitiesList
    .filter(i => i.si === currentSide.value)
    .map(i => i.na)
})
const sideColor = computed(() => {
  return sideList.find(i => i.value === currentSide.value)?.color || '#fff'
})
const currentEntityId = inject('currentEntityId', ref(''))
const currentSide = inject('currentSide', ref('red'))
/** 单击实体 */
const handleNodeClick = (na: string) => {
  currentEntityId.value = na
}
/** 双击实体 */
const handleDbNodeClick = (na: string) => {
  emit('gDbClick', na)
}

watch(
  () => currentEntityId.value,
  val => {
    if (!val) return
    const entity = window.viewer.entities.getById(val)
    const properties = entity && entity.properties
    if (!properties) return
    currentSide.value = properties.si.getValue()
  }
)

watch(
  () => store.state.simulationConfig.taskStatus,
  val => {
    if (val === 4) {
      // currentEntityId.value = ''
      currentSide.value = 'red'
    }
  }
)
watch(
  () => props.entitiesList,
  val => {
    if (Array.isArray(val) && val.length && !currentEntityId.value) {
      // 优先选当前阵营下的第一个
      let first = val.find(i => i.si === currentSide.value)
      if (!first) {
        // 没有当前阵营的，选其他阵营第一个
        first = val[0]
      }
      if (first) {
        currentEntityId.value = first.na
      }
    }
  },
  { immediate: true, deep: true }
)
</script>

<style lang="less" scoped>
.entityCardBox {
  height: calc(100% - 303px - 200px);
  background: url('/image/box_bg.png') no-repeat 0px 37px;
  background-size: 100% 100%;
  .title {
    height: 37px;
    font-size: 16px;
    font-family: Bold;
    font-style: oblique;
    align-items: center;
    padding-left: 35px;
    padding-bottom: 4px;
    background: url('/image/title_bg.png') no-repeat;
    span {
      background: url('/image/title_font_bg.png') no-repeat 15px 5px;
    }
  }
  .contentBox {
    height: calc(100% - 32px);
    .tabs {
      .tab {
        width: 87px;
        height: 30px;
        line-height: 30px;
        background: #033a5a;
        border: solid 1px var(--app-border-color);
        border-bottom: none;
        cursor: pointer;
        text-align: center;
        margin-right: 10px;
        border-top-left-radius: 2px;
        border-top-right-radius: 2px;
        font-weight: 600;
      }
      .active {
        background: #125d8d;
      }
    }
    .entityList {
      height: calc(100% - 30px - 5px);
      border: 1px solid var(--app-border-color);
      overflow-y: auto;
      padding: 5px 10px;
      .entity {
        padding-left: 5px;
        cursor: pointer;
        line-height: 28px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .entity:hover {
        background: #015586;
      }
    }
    .emptyBox {
      padding-top: 50px;
      height: calc(100% - 30px - 5px);
      border: 1px solid var(--app-border-color);
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }
}
</style>
