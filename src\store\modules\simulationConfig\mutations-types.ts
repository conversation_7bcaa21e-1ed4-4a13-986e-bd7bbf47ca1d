export enum SimulationConfigMutationTypes {
  SET_TASK_STATUS = 'SET_TASK_STATUS',
  SET_RUNMODE_STATUS = 'SET_RUNMODE_STATUS',
  SET_SIM_TIMESTAMP = 'SET_SIM_TIMESTAMP',
  SET_RED_STATE = 'SET_RED_STATE',
  SET_BLUE_STATE = 'SET_BLUE_STATE',
  SET_LABEL_STATE = 'SET_LABEL_STATE',
  SET_COMMUNICATION_STATE = 'SET_COMMUNICATION_STATE',
  SET_ROUTE_STATE = 'SET_ROUTE_STATE',
  SET_PROB_STATE = 'SET_PROB_STATE',
  SET_FIRE_STATE = 'SET_FIRE_STATE',
  SET_TRAIL_STATE = 'SET_TRAIL_STATE',
  SET_SENSOR_STATE = 'SET_SENSOR_STATE',
  SET_ENTITY_CONFIG_COMMUNICATION = 'SET_ENTITY_CONFIG_COMMUNICATION',
  SET_ENTITY_CONFIG_PROB = 'SET_ENTITY_CONFIG_PROB',
  SET_ENTITY_CONFIG_FIRE = 'SET_ENTITY_CONFIG_FIRE',
  SET_ENTITY_CONFIG_TRAIL = 'SET_ENTITY_CONFIG_TRAIL',
  SET_ENTITY_CONFIG_LABEL = 'SET_ENTITY_CONFIG_LABEL',
  SET_ENTITY_CONFIG_SENSOR = 'SET_ENTITY_CONFIG_SENSOR',
}