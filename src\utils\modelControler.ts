/*
 * @Author: 老范
 * @Date: 2025-04-01 15:25:08
 * @LastEditors: tzs
 * @LastEditTime: 2025-08-11 10:21:56
 * @Description: 请填写简介
 */
import { TrailManager } from './primitive'
import { useStore } from 'vuex'
import { getStore } from './store-utils'

// 类型定义
type ModelUpdateData = {
  na: string
  ic: string
  lo: number
  la: number
  al: number
  si: string
  ya?: number // yaw
  pi?: number //pitch
  ro?: number //roll
  type: string
}

type ModelConfig = {
  maxTrailPoints: number // 尾迹最大点数
  trailWidth: number // 尾迹线宽
  trailColor: Cesium.Color
  modelCacheSize: number // 模型池缓存数量
  lodTransitionMargin: number // 防止LOD频繁切换的缓冲距离
  maxTrailPointPoolSize: number // 对象池最大容量
  trailPointPoolInitialSize: number // 初始池大小
  lodDistanceThresholds: {
    // LOD距离阈值
    high: number
    medium: number
    low: number
  }
}

export class ModelManager {
  private viewer: Cesium.Viewer
  private config: ModelConfig
  // 模型池
  private modelPool = new Map<string, Cesium.Model>()

  private positionPool = new Map<string, Cesium.Cartesian3>()

  // 待回收模型队列
  private recycleQueue: string[] = []
  // trailSystem: TrailSystem;
  trailPrimitive: any
  effectManager: any

  private modelSizeConfig = {
    minimumPixelSize: 128,
    maximumScale: 512,
  }

  constructor(
    viewer: Cesium.Viewer,
    effectManager: any,
    config?: Partial<ModelConfig>
  ) {
    this.viewer = viewer
    this.effectManager = effectManager
    this.config = {
      maxTrailPoints: 10,
      trailWidth: 3,
      trailColor: Cesium.Color.CYAN.withAlpha(0.7),
      modelCacheSize: 2000,
      lodTransitionMargin: 500, // 单位：米
      maxTrailPointPoolSize: 5000, // 最多缓存5000个点对象
      trailPointPoolInitialSize: 10, // 初始化时预创建1000个
      lodDistanceThresholds: {
        high: 5000, // 5km内高精度模型
        medium: 10000, // 10km中精度
        low: 20000, // 20km以上低精度
      },
      ...config,
    }
    this.trailPrimitive = new TrailManager(viewer)
  }

  // 批量更新模型 (主入口)
  public async batchUpdate(data: ModelUpdateData[]) {
    data.forEach(item => {
      // 1. 位置转换
      const position = Cesium.Cartesian3.fromDegrees(
        item.lo,
        item.la,
        item.al | 0
      )
      // 2. 模型存在性检查
      if (this.modelPool.has(item.na)) {
        this.updateExistingModel(item.na, position, item)
        this.trailPrimitive.updateTrail(item.na, [
          { lo: item.lo, la: item.la, al: item.al | 0 },
        ])

        // 计算模型姿态
        const orientation = this.calculateOrientation(
          position,
          item.ya ?? 0,
          item.pi ?? 0,
          item.ro ?? 0
        )

        // 更新该模型身上的sensor特效位置
        this.updateModelSensorEffects(item.na, position, orientation)
      }
    })
  }

  // 创建新模型
  public async createNewModel(item: ModelUpdateData) {
    const modelColor =
      item.si === 'red'
        ? Cesium.Color.fromCssColorString('#ff1212')
        : Cesium.Color.fromCssColorString('#6d6dff')
    try {
      const position = Cesium.Cartesian3.fromDegrees(
        item.lo,
        item.la,
        item.al | 0
      )
      const model = await this.loadModelWithLod(item.ic)
      // 计算姿态四元数
      const orientation = this.calculateOrientation(
        position,
        item.ya,
        item.pi,
        item.ro
      )
      const entity = this.viewer.entities.add({
        id: item.na,
        position: new Cesium.CallbackProperty(() => position, false),
        model: {
          uri: model.uri,
          minimumPixelSize: this.modelSizeConfig.minimumPixelSize,
          maximumScale: this.modelSizeConfig.maximumScale,
          color: modelColor,
          show: this.readModelDisplayConfig(item.si),
        },
        orientation, // 只在entity上设置orientation
        label: {
          text: item.na,
          show: this.readModelDisplayConfig(item.si),
          font: '16px sans-serif',
          showBackground: false,
          fillColor: modelColor,
          pixelOffset: new Cesium.Cartesian3(0, -30, 0),
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          outlineColor: Cesium.Color.WHITE,
          outlineWidth: 1.5,
        },
        properties: {
          ...item,
        },
      })
      // 加入对象池
      this.modelPool.set(item.na, entity.model)
      // 管理回收队列
      if (this.recycleQueue.length >= this.config.modelCacheSize) {
        const oldId = this.recycleQueue.shift()!

        this.destroyModel(oldId)
      }

      this.recycleQueue.push(item.na)
    } catch (error) {
      console.error(`Failed to load model ${item.type}:`, error)
    }
  }

  // 添加辅助方法
  private modelCache = new Map<string, Promise<void>>()

  private async loadModelWithCache(uri: string): Promise<void> {
    // 已加载的模型直接返回
    if (this.modelCache.has(uri)) {
      return this.modelCache.get(uri)!
    }

    const promise = new Promise<void>((resolve, reject) => {
      // 使用Cesium内置资源加载器
      Cesium.Resource.fetchArrayBuffer(uri)
        .then(() => resolve())
        .catch(reject)
    })

    this.modelCache.set(uri, promise)
    return promise
  }

  private async loadModelWithLod(type: string) {
    console.log(type)
    // const url = `${(window as any).GVJ.URLS.pluginServer}api/v1/model3d/view?name=${type}`
    const url = `${(window as any).GVJ.URLS.pluginServer}api/v1/model3d/glb/view?name=${type}`
    return {
      uri: `http://localhost:6789/Cesium-example/models/DJmavic3.glb`,
    }
  }

  // 更新现有模型
  private updateExistingModel(
    id: string,
    newPosition: Cesium.Cartesian3,
    item: {
      na?: string
      lo?: number
      la?: number
      al?: number
      ya: any
      pi: any
      ro: any
      type?: string
    }
  ) {
    const entity = this.viewer.entities.getById(id)
    if (entity) {
      entity.position = newPosition // 让Cesium内部处理矩阵计算
      this.positionPool.set(id, newPosition.clone())
      if (item.ya) {
        const orientation = this.calculateOrientation(
          newPosition,
          item.ya ?? 0,
          item.pi ?? 0,
          item.ro ?? 0
        )
        entity.orientation = new Cesium.ConstantProperty(orientation)
      }
      entity.properties = {
        ...item,
      }
    }
  }

  // 计算模型方向
  private calculateOrientation(
    position: Cesium.Cartesian3,
    yaw: number | undefined,
    pitch: number | undefined,
    roll: number | undefined
  ) {
    return Cesium.Transforms.headingPitchRollQuaternion(
      position,
      Cesium.HeadingPitchRoll.fromDegrees(yaw, pitch, roll),
      Cesium.Ellipsoid.WGS84,
      Cesium.Transforms.northWestUpToFixedFrame
    )
  }

  // 销毁模型
  public destroyModel(id: string) {
    const model = this.modelPool.get(id)
    const trail = this.trailPrimitive.trails.get(id)
    if (model) {
      this.viewer.entities.removeById(id)
      this.modelPool.delete(id)
      this.positionPool.delete(id)
    }
    if (trail) {
      this.trailPrimitive.removeTrail(id)
    }
    this.effectManager.destroyEffectByModelId(id)
  }

  // 重置所有模型
  public reset() {
    this.modelPool.forEach((_, id) => {
      this.destroyModel(id)
      this.trailPrimitive.removeTrail(id)
    })
    this.effectManager.destroy()
  }

  /**
   * 更新模型尺寸配置
   * @param sizeLevel 尺寸级别（1-5）
   */
  public updateModelSize(sizeLevel: number) {
    // 确保级别在1-5范围内
    const level = Math.max(1, Math.min(5, Math.floor(sizeLevel)))

    // 计算新的尺寸参数
    // 级别1: 默认值
    // 级别2-5: 线性增加
    this.modelSizeConfig = {
      minimumPixelSize: 128 * level,
      maximumScale: 512 * level,
    }

    // 更新所有现有模型
    const entities = this.viewer.entities.values
    for (let i = 0; i < entities.length; i++) {
      const entity = entities[i]
      if (entity.model) {
        entity.model.minimumPixelSize = new Cesium.ConstantProperty(
          this.modelSizeConfig.minimumPixelSize
        )
        entity.model.maximumScale = new Cesium.ConstantProperty(
          this.modelSizeConfig.maximumScale
        )
      }
    }

    console.log(
      `模型尺寸已更新: 级别=${level}, 最小像素=${this.modelSizeConfig.minimumPixelSize}, 最大缩放=${this.modelSizeConfig.maximumScale}`
    )

    return this.modelSizeConfig
  }

  /** 读取模型显示/隐藏配置 */
  private readModelDisplayConfig(si: string) {
    const store = getStore()
    const { redState, blueState } = store.state.simulationConfig
    if (si === 'red') return redState === 'checked'
    if (si === 'blue') return blueState === 'checked'
    return false
  }

  // 新增方法：更新模型的sensor特效
  private updateModelSensorEffects(
    modelId: string,
    newPosition: Cesium.Cartesian3,
    orientation: Cesium.Quaternion
  ) {
    // 查找该模型的所有sensor特效
    this.effectManager.effects.forEach((effect, effectId) => {
      if (effect.config.modelId === modelId && effect.type === 'sensor') {
        // 更新特效位置和姿态
        this.effectManager.updateEffectPosition(
          effectId,
          newPosition,
          orientation
        )
      }
    })
  }
}
