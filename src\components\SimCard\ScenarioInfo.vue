<template>
  <div class="scenarioBox">
    <el-row class="title">
      <span>方案信息</span>
    </el-row>
    <div class="contentBox">
      <el-row class="item">
        <div class="label">方案名称</div>
        <div class="value" :title="scenarioInfo.scenarioName || ''">
          {{ scenarioInfo.scenarioName || '-' }}
        </div>
      </el-row>
      <el-row class="item description-item">
        <div class="label">方案描述</div>
        <div
          class="value description-value"
          :title="scenarioInfo.scenarioDescribe || ''"
        >
          {{ scenarioInfo.scenarioDescribe || '-' }}
        </div>
      </el-row>
      <el-row class="item">
        <div class="label">运行模式</div>
        <div class="value">
          {{ scenarioInfo.modeText || '-' }}
        </div>
      </el-row>
      <el-row class="timeline-row">
        <p>仿真持续时间</p>
        <el-row class="duration">{{
          formatDuration(scenarioInfo.realTime)
        }}</el-row>
        <el-row class="item">
          <div class="label">仿真时间</div>
          <div class="value date">
            {{
              props.scenarioInfo?.startTime &&
              scenarioInfo?.realTime !== undefined
                ? dayjs(
                    props.scenarioInfo.startTime + scenarioInfo.realTime * 1000
                  )
                    .subtract(8, 'hour')
                    .format('YYYY-MM-DD HH:mm:ss')
                : currentTime
            }}
          </div>
        </el-row>
        <el-row class="item">
          <div class="label">天文时间</div>
          <div class="value date">
            {{ currentTime }}
          </div>
        </el-row>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import duration from 'dayjs/plugin/duration'
import { ref, onMounted, onUnmounted } from 'vue'

// 配置dayjs插件
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(duration)

// 将秒数转换为 YYYY-MM-DD HH:mm:ss 格式
const formatDuration = (seconds: number): string => {
  // 使用dayjs duration处理秒数
  const dur = dayjs.duration(seconds, 'seconds')
  const years = Math.floor(dur.asYears())
  const months = dur.months()
  const days = dur.days()
  const hours = dur.hours()
  const minutes = dur.minutes()
  const secs = dur.seconds()

  return `${years.toString().padStart(4, '0')}-${months
    .toString()
    .padStart(2, '0')}-${days.toString().padStart(2, '0')} ${hours
    .toString()
    .padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs
    .toString()
    .padStart(2, '0')}`
}

const props = defineProps({
  scenarioInfo: {
    type: Object,
    default: '',
  },
})

// 添加当前时间的响应式变量
const currentTime = ref(dayjs().format('YYYY-MM-DD HH:mm:ss'))

// 添加定时器变量
let timer: NodeJS.Timeout | null = null

// 组件挂载时启动定时器
onMounted(() => {
  // 每秒更新一次时间
  timer = setInterval(() => {
    currentTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss')
  }, 1000)
})

// 组件卸载时清除定时器
onUnmounted(() => {
  // 组件卸载时清除定时器
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style lang="less" scoped>
.scenarioBox {
  width: 336px;
  height: 332px;
  background: url('/image/info_bg.png') no-repeat;
  background-size: 100% 100%;
  border-left: none;
  .title {
    height: 32px;
    font-size: 16px;
    font-family: Bold;
    font-style: oblique;
    align-items: center;
    padding-left: 35px;
    background: url('/image/title_font_bg.png') no-repeat 15px 5px;
  }
  .contentBox {
    font-size: 15px;
    padding: 10px;

    .item {
      display: flex;
      align-items: center;
      margin-bottom: 9px;

      .label {
        flex-shrink: 0; // 防止标签缩小
        white-space: nowrap;
        height: 30px;
        line-height: 30px;
        margin-right: 10px;
      }

      .value {
        width: 237px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        box-sizing: border-box;
        font-weight: 600;
        background: url('/image/info_font_bg.png') no-repeat;
        height: 30px;
        padding-left: 5px;
        line-height: 30px;
      }
      .value.date {
        padding-left: 8px;
        background: url('/image/info_font_bg.png') no-repeat;
        background-size: 100% 100%;
        height: 30px;
      }
      .value.description-value {
        height: 50px;
        white-space: normal; // 允许换行
        display: -webkit-box;
        -webkit-line-clamp: 2; // 限制为两行
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        background-size: 100% 100%;
      }
    }
    .item.description-item {
      align-items: flex-start;
    }

    .duration {
      font-family: Regular;
      font-size: 22px;
      justify-content: center;
      margin-bottom: 5px;
      font-weight: 600;

      text-shadow: 0 0 5px #357a92, /* 内层淡光 */ 0 0 10px #357a92,
        /* 中层亮光 */ 0 0 15px #357a92; /* 外层扩散光 */
    }
    // 添加新的样式
    .timeline-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-direction: column;
      width: 100%;
      background: url('/image/time_bg.png') no-repeat;
      .date {
        width: 217px;
      }
      p {
        height: 30px;
        line-height: 30px;
      }
    }

    .timeline-img {
      width: 23%; // 调整图片宽度
      height: 3px;
      flex-shrink: 0;
    }
    .timeline-img.left {
      transform: rotate(180deg);
    }
    .timeline-text {
      img {
        margin: 0 5px;
      }
      flex-grow: 1;
      margin: 0;
      white-space: nowrap;
      align-items: center;
    }
  }
}
</style>
