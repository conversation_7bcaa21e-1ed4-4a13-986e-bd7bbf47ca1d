export class CesiumManager {
  private viewer: any = null
  private GV: any = null

  constructor() {
    this.GV = (window as any).GV
  }

  public createView(containerId: string) {
    this.viewer = new this.GV.GeoCanvas(containerId, {
      baseLayerPicker: false,
      shouldAnimate: true, //动画开启
    })

    this.viewer.imageryLayers.addImageryProvider(
      new Cesium.UrlTemplateImageryProvider({
        url: `${(window as any).GVJ.URLS.mapUrl}`,
        minimumLevel: 0,
        maximumLevel: 8,
      })
    )

    ;(window as any).viewer = this.viewer
    return this.viewer
  }

  public getViewer() {
    return this.viewer
  }

  /** 重置相机 */
  public resetCamera(maxDistance: number, center: number[]) {
    // 北京坐标: 经度 116.4074, 纬度 39.9042
    // 高度设置为 20000000 米 (2万公里) 以便能看到整个地球
    this.viewer.iCamera.flyTo(
      new this.GV.ViewPoint(116.4074, 39.9042, 20000000, 0, -90, 0),
      0 // 飞行时间
    )
    this.viewerToAnodic(maxDistance, center)
    this.viewer.trackedEntity = null
  }

  /** 相机飞行至场景上空 */
  public viewerToAnodic(maxDistance: number, center: number[]) {
    const scale = (maxDistance * 1000) / 2
    const earthRadius = 6371000
    const alt =
      Math.sqrt(3) * scale +
      Math.sqrt(earthRadius * earthRadius - scale * scale) -
      earthRadius

    // 动态偏移高度，例如增加场景 scale 的 2.5倍
    const offset = scale * 2.5

    console.log('🚀 ~ viewerToAnodic ~ center:', center)
    this.viewer.iCamera.flyTo(new this.GV.ViewPoint(...center, alt + offset), 0)
  }

  /** 跟踪实体 */
  public focusEntity(entityId: string) {
    this.viewer.trackedEntity = this.viewer.entities.getById(entityId)
  }

  /** 全屏 */
  public toggleFullscreen(containerId: string) {
    const dom = document.getElementById(containerId)
    if (!dom) return false

    if (!document.fullscreenElement) {
      dom.requestFullscreen()
      return true
    } else {
      document.exitFullscreen()
      return false
    }
  }
}